{"version": 3, "file": "index.cjs", "sources": ["../src/utils.ts", "../src/youtube.ts"], "sourcesContent": ["export const YOUTUBE_REGEX = /^((?:https?:)?\\/\\/)?((?:www|m|music)\\.)?((?:youtube\\.com|youtu.be|youtube-nocookie\\.com))(\\/(?:[\\w-]+\\?v=|embed\\/|v\\/)?)([\\w-]+)(\\S+)?$/\nexport const YOUTUBE_REGEX_GLOBAL = /^((?:https?:)?\\/\\/)?((?:www|m|music)\\.)?((?:youtube\\.com|youtu.be|youtube-nocookie\\.com))(\\/(?:[\\w-]+\\?v=|embed\\/|v\\/)?)([\\w-]+)(\\S+)?$/g\n\nexport const isValidYoutubeUrl = (url: string) => {\n  return url.match(YOUTUBE_REGEX)\n}\n\nexport interface GetEmbedUrlOptions {\n  url: string;\n  allowFullscreen?: boolean;\n  autoplay?: boolean;\n  ccLanguage?:string;\n  ccLoadPolicy?:boolean;\n  controls?: boolean;\n  disableKBcontrols?: boolean,\n  enableIFrameApi?: boolean;\n  endTime?: number;\n  interfaceLanguage?: string;\n  ivLoadPolicy?: number;\n  loop?: boolean;\n  modestBranding?: boolean;\n  nocookie?: boolean;\n  origin?: string;\n  playlist?: string;\n  progressBarColor?: string;\n  startAt?: number;\n  rel?: number;\n}\n\nexport const getYoutubeEmbedUrl = (nocookie?: boolean, isPlaylist?:boolean) => {\n  if (isPlaylist) {\n    return 'https://www.youtube-nocookie.com/embed/videoseries?list='\n  }\n  return nocookie ? 'https://www.youtube-nocookie.com/embed/' : 'https://www.youtube.com/embed/'\n}\n\nconst getYoutubeVideoOrPlaylistId = (\n  url: URL,\n): { id: string; isPlaylist?: boolean } | null => {\n  if (url.searchParams.has('v')) {\n    return { id: url.searchParams.get('v')! }\n  }\n\n  if (\n    url.hostname === 'youtu.be'\n    || url.pathname.includes('shorts')\n    || url.pathname.includes('live')\n  ) {\n    return { id: url.pathname.split('/').pop()! }\n  }\n\n  if (url.searchParams.has('list')) {\n    return { id: url.searchParams.get('list')!, isPlaylist: true }\n  }\n\n  return null\n}\n\nexport const getEmbedUrlFromYoutubeUrl = (options: GetEmbedUrlOptions) => {\n  const {\n    url,\n    allowFullscreen,\n    autoplay,\n    ccLanguage,\n    ccLoadPolicy,\n    controls,\n    disableKBcontrols,\n    enableIFrameApi,\n    endTime,\n    interfaceLanguage,\n    ivLoadPolicy,\n    loop,\n    modestBranding,\n    nocookie,\n    origin,\n    playlist,\n    progressBarColor,\n    startAt,\n    rel,\n  } = options\n\n  if (!isValidYoutubeUrl(url)) {\n    return null\n  }\n\n  // if is already an embed url, return it\n  if (url.includes('/embed/')) {\n    return url\n  }\n\n  const urlObject = new URL(url)\n  const { id, isPlaylist } = getYoutubeVideoOrPlaylistId(urlObject) ?? {}\n\n  if (!id) { return null }\n\n  const embedUrl = new URL(`${getYoutubeEmbedUrl(nocookie, isPlaylist)}${id}`)\n\n  if (urlObject.searchParams.has('t')) {\n    embedUrl.searchParams.set('start', urlObject.searchParams.get('t')!.replaceAll('s', ''))\n  }\n\n  if (allowFullscreen === false) {\n    embedUrl.searchParams.set('fs', '0')\n  }\n\n  if (autoplay) {\n    embedUrl.searchParams.set('autoplay', '1')\n  }\n\n  if (ccLanguage) {\n    embedUrl.searchParams.set('cc_lang_pref', ccLanguage)\n  }\n\n  if (ccLoadPolicy) {\n    embedUrl.searchParams.set('cc_load_policy', '1')\n  }\n\n  if (!controls) {\n    embedUrl.searchParams.set('controls', '0')\n  }\n\n  if (disableKBcontrols) {\n    embedUrl.searchParams.set('disablekb', '1')\n  }\n\n  if (enableIFrameApi) {\n    embedUrl.searchParams.set('enablejsapi', '1')\n  }\n\n  if (endTime) {\n    embedUrl.searchParams.set('end', endTime.toString())\n  }\n\n  if (interfaceLanguage) {\n    embedUrl.searchParams.set('hl', interfaceLanguage)\n  }\n\n  if (ivLoadPolicy) {\n    embedUrl.searchParams.set('iv_load_policy', ivLoadPolicy.toString())\n  }\n\n  if (loop) {\n    embedUrl.searchParams.set('loop', '1')\n  }\n\n  if (modestBranding) {\n    embedUrl.searchParams.set('modestbranding', '1')\n  }\n\n  if (origin) {\n    embedUrl.searchParams.set('origin', origin)\n  }\n\n  if (playlist) {\n    embedUrl.searchParams.set('playlist', playlist)\n  }\n\n  if (startAt) {\n    embedUrl.searchParams.set('start', startAt.toString())\n  }\n\n  if (progressBarColor) {\n    embedUrl.searchParams.set('color', progressBarColor)\n  }\n\n  if (rel !== undefined) {\n    embedUrl.searchParams.set('rel', rel.toString())\n  }\n\n  return embedUrl.toString()\n}\n", "import { mergeAttributes, Node, nodePasteRule } from '@tiptap/core'\n\nimport { getEmbedUrlFromYoutubeUrl, isValidYoutubeUrl, YOUTUBE_REGEX_GLOBAL } from './utils.js'\n\nexport interface YoutubeOptions {\n  /**\n   * Controls if the paste handler for youtube videos should be added.\n   * @default true\n   * @example false\n   */\n  addPasteHandler: boolean;\n\n  /**\n   * Controls if the youtube video should be allowed to go fullscreen.\n   * @default true\n   * @example false\n   */\n  allowFullscreen: boolean;\n\n  /**\n   * Controls if the youtube video should autoplay.\n   * @default false\n   * @example true\n   */\n  autoplay: boolean;\n\n  /**\n   * The language of the captions shown in the youtube video.\n   * @default undefined\n   * @example 'en'\n   */\n  ccLanguage?: string;\n\n  /**\n   * Controls if the captions should be shown in the youtube video.\n   * @default undefined\n   * @example true\n   */\n  ccLoadPolicy?: boolean;\n\n  /**\n   * Controls if the controls should be shown in the youtube video.\n   * @default true\n   * @example false\n   */\n  controls: boolean;\n\n  /**\n   * Controls if the keyboard controls should be disabled in the youtube video.\n   * @default false\n   * @example true\n   */\n  disableKBcontrols: boolean;\n\n  /**\n   * Controls if the iframe api should be enabled in the youtube video.\n   * @default false\n   * @example true\n   */\n  enableIFrameApi: boolean;\n\n  /**\n   * The end time of the youtube video.\n   * @default 0\n   * @example 120\n   */\n  endTime: number;\n\n  /**\n   * The height of the youtube video.\n   * @default 480\n   * @example 720\n   */\n  height: number;\n\n  /**\n   * The language of the youtube video.\n   * @default undefined\n   * @example 'en'\n   */\n  interfaceLanguage?: string;\n\n  /**\n   * Controls if the video annotations should be shown in the youtube video.\n   * @default 0\n   * @example 1\n   */\n  ivLoadPolicy: number;\n\n  /**\n   * Controls if the youtube video should loop.\n   * @default false\n   * @example true\n   */\n  loop: boolean;\n\n  /**\n   * Controls if the youtube video should show a small youtube logo.\n   * @default false\n   * @example true\n   */\n  modestBranding: boolean;\n\n  /**\n   * The HTML attributes for a youtube video node.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>;\n\n  /**\n   * Controls if the youtube node should be inline or not.\n   * @default false\n   * @example true\n   */\n  inline: boolean;\n\n  /**\n   * Controls if the youtube video should be loaded from youtube-nocookie.com.\n   * @default false\n   * @example true\n   */\n  nocookie: boolean;\n\n  /**\n   * The origin of the youtube video.\n   * @default ''\n   * @example 'https://tiptap.dev'\n   */\n  origin: string;\n\n  /**\n   * The playlist of the youtube video.\n   * @default ''\n   * @example 'PLQg6GaokU5CwiVmsZ0dZm6VeIg0V5z1tK'\n   */\n  playlist: string;\n\n  /**\n   * The color of the youtube video progress bar.\n   * @default undefined\n   * @example 'red'\n   */\n  progressBarColor?: string;\n\n  /**\n   * The width of the youtube video.\n   * @default 640\n   * @example 1280\n   */\n  width: number;\n\n  /**\n   * Controls if the related youtube videos at the end are from the same channel.\n   * @default 1\n   * @example 0\n   */\n  rel: number;\n}\n\n/**\n * The options for setting a youtube video.\n */\ntype SetYoutubeVideoOptions = { src: string, width?: number, height?: number, start?: number }\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    youtube: {\n      /**\n       * Insert a youtube video\n       * @param options The youtube video attributes\n       * @example editor.commands.setYoutubeVideo({ src: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ' })\n       */\n      setYoutubeVideo: (options: SetYoutubeVideoOptions) => ReturnType,\n    }\n  }\n}\n\n/**\n * This extension adds support for youtube videos.\n * @see https://www.tiptap.dev/api/nodes/youtube\n */\nexport const Youtube = Node.create<YoutubeOptions>({\n  name: 'youtube',\n\n  addOptions() {\n    return {\n      addPasteHandler: true,\n      allowFullscreen: true,\n      autoplay: false,\n      ccLanguage: undefined,\n      ccLoadPolicy: undefined,\n      controls: true,\n      disableKBcontrols: false,\n      enableIFrameApi: false,\n      endTime: 0,\n      height: 480,\n      interfaceLanguage: undefined,\n      ivLoadPolicy: 0,\n      loop: false,\n      modestBranding: false,\n      HTMLAttributes: {},\n      inline: false,\n      nocookie: false,\n      origin: '',\n      playlist: '',\n      progressBarColor: undefined,\n      width: 640,\n      rel: 1,\n    }\n  },\n\n  inline() {\n    return this.options.inline\n  },\n\n  group() {\n    return this.options.inline ? 'inline' : 'block'\n  },\n\n  draggable: true,\n\n  addAttributes() {\n    return {\n      src: {\n        default: null,\n      },\n      start: {\n        default: 0,\n      },\n      width: {\n        default: this.options.width,\n      },\n      height: {\n        default: this.options.height,\n      },\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: 'div[data-youtube-video] iframe',\n      },\n    ]\n  },\n\n  addCommands() {\n    return {\n      setYoutubeVideo: (options: SetYoutubeVideoOptions) => ({ commands }) => {\n        if (!isValidYoutubeUrl(options.src)) {\n          return false\n        }\n\n        return commands.insertContent({\n          type: this.name,\n          attrs: options,\n        })\n      },\n    }\n  },\n\n  addPasteRules() {\n    if (!this.options.addPasteHandler) {\n      return []\n    }\n\n    return [\n      nodePasteRule({\n        find: YOUTUBE_REGEX_GLOBAL,\n        type: this.type,\n        getAttributes: match => {\n          return { src: match.input }\n        },\n      }),\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    const embedUrl = getEmbedUrlFromYoutubeUrl({\n      url: HTMLAttributes.src,\n      allowFullscreen: this.options.allowFullscreen,\n      autoplay: this.options.autoplay,\n      ccLanguage: this.options.ccLanguage,\n      ccLoadPolicy: this.options.ccLoadPolicy,\n      controls: this.options.controls,\n      disableKBcontrols: this.options.disableKBcontrols,\n      enableIFrameApi: this.options.enableIFrameApi,\n      endTime: this.options.endTime,\n      interfaceLanguage: this.options.interfaceLanguage,\n      ivLoadPolicy: this.options.ivLoadPolicy,\n      loop: this.options.loop,\n      modestBranding: this.options.modestBranding,\n      nocookie: this.options.nocookie,\n      origin: this.options.origin,\n      playlist: this.options.playlist,\n      progressBarColor: this.options.progressBarColor,\n      startAt: HTMLAttributes.start || 0,\n      rel: this.options.rel,\n    })\n\n    HTMLAttributes.src = embedUrl\n\n    return [\n      'div',\n      { 'data-youtube-video': '' },\n      [\n        'iframe',\n        mergeAttributes(\n          this.options.HTMLAttributes,\n          {\n            width: this.options.width,\n            height: this.options.height,\n            allowfullscreen: this.options.allowFullscreen,\n            autoplay: this.options.autoplay,\n            ccLanguage: this.options.ccLanguage,\n            ccLoadPolicy: this.options.ccLoadPolicy,\n            disableKBcontrols: this.options.disableKBcontrols,\n            enableIFrameApi: this.options.enableIFrameApi,\n            endTime: this.options.endTime,\n            interfaceLanguage: this.options.interfaceLanguage,\n            ivLoadPolicy: this.options.ivLoadPolicy,\n            loop: this.options.loop,\n            modestBranding: this.options.modestBranding,\n            origin: this.options.origin,\n            playlist: this.options.playlist,\n            progressBarColor: this.options.progressBarColor,\n            rel: this.options.rel,\n          },\n          HTMLAttributes,\n        ),\n      ],\n    ]\n  },\n})\n"], "names": ["Node", "nodePasteRule", "mergeAttributes"], "mappings": ";;;;;;AAAO,MAAM,aAAa,GAAG,yIAAyI;AAC/J,MAAM,oBAAoB,GAAG,0IAA0I;AAEvK,MAAM,iBAAiB,GAAG,CAAC,GAAW,KAAI;AAC/C,IAAA,OAAO,GAAG,CAAC,KAAK,CAAC,aAAa,CAAC;AACjC,CAAC;AAwBM,MAAM,kBAAkB,GAAG,CAAC,QAAkB,EAAE,UAAmB,KAAI;IAC5E,IAAI,UAAU,EAAE;AACd,QAAA,OAAO,0DAA0D;;IAEnE,OAAO,QAAQ,GAAG,yCAAyC,GAAG,gCAAgC;AAChG,CAAC;AAED,MAAM,2BAA2B,GAAG,CAClC,GAAQ,KACuC;IAC/C,IAAI,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;AAC7B,QAAA,OAAO,EAAE,EAAE,EAAE,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAE,EAAE;;AAG3C,IAAA,IACE,GAAG,CAAC,QAAQ,KAAK;AACd,WAAA,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ;WAC9B,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,EAChC;AACA,QAAA,OAAO,EAAE,EAAE,EAAE,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAG,EAAE;;IAG/C,IAAI,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;AAChC,QAAA,OAAO,EAAE,EAAE,EAAE,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAE,EAAE,UAAU,EAAE,IAAI,EAAE;;AAGhE,IAAA,OAAO,IAAI;AACb,CAAC;AAEM,MAAM,yBAAyB,GAAG,CAAC,OAA2B,KAAI;;AACvE,IAAA,MAAM,EACJ,GAAG,EACH,eAAe,EACf,QAAQ,EACR,UAAU,EACV,YAAY,EACZ,QAAQ,EACR,iBAAiB,EACjB,eAAe,EACf,OAAO,EACP,iBAAiB,EACjB,YAAY,EACZ,IAAI,EACJ,cAAc,EACd,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,OAAO,EACP,GAAG,GACJ,GAAG,OAAO;AAEX,IAAA,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,EAAE;AAC3B,QAAA,OAAO,IAAI;;;AAIb,IAAA,IAAI,GAAG,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;AAC3B,QAAA,OAAO,GAAG;;AAGZ,IAAA,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC;AAC9B,IAAA,MAAM,EAAE,EAAE,EAAE,UAAU,EAAE,GAAG,CAAA,EAAA,GAAA,2BAA2B,CAAC,SAAS,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,EAAE;IAEvE,IAAI,CAAC,EAAE,EAAE;AAAE,QAAA,OAAO,IAAI;;AAEtB,IAAA,MAAM,QAAQ,GAAG,IAAI,GAAG,CAAC,GAAG,kBAAkB,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAA,EAAG,EAAE,CAAA,CAAE,CAAC;IAE5E,IAAI,SAAS,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;QACnC,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,SAAS,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAE,CAAC,UAAU,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;;AAG1F,IAAA,IAAI,eAAe,KAAK,KAAK,EAAE;QAC7B,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;;IAGtC,IAAI,QAAQ,EAAE;QACZ,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC;;IAG5C,IAAI,UAAU,EAAE;QACd,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,cAAc,EAAE,UAAU,CAAC;;IAGvD,IAAI,YAAY,EAAE;QAChB,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,gBAAgB,EAAE,GAAG,CAAC;;IAGlD,IAAI,CAAC,QAAQ,EAAE;QACb,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC;;IAG5C,IAAI,iBAAiB,EAAE;QACrB,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC;;IAG7C,IAAI,eAAe,EAAE;QACnB,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,aAAa,EAAE,GAAG,CAAC;;IAG/C,IAAI,OAAO,EAAE;AACX,QAAA,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,QAAQ,EAAE,CAAC;;IAGtD,IAAI,iBAAiB,EAAE;QACrB,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,EAAE,iBAAiB,CAAC;;IAGpD,IAAI,YAAY,EAAE;AAChB,QAAA,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,gBAAgB,EAAE,YAAY,CAAC,QAAQ,EAAE,CAAC;;IAGtE,IAAI,IAAI,EAAE;QACR,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC;;IAGxC,IAAI,cAAc,EAAE;QAClB,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,gBAAgB,EAAE,GAAG,CAAC;;IAGlD,IAAI,MAAM,EAAE;QACV,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC;;IAG7C,IAAI,QAAQ,EAAE;QACZ,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC;;IAGjD,IAAI,OAAO,EAAE;AACX,QAAA,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,QAAQ,EAAE,CAAC;;IAGxD,IAAI,gBAAgB,EAAE;QACpB,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,gBAAgB,CAAC;;AAGtD,IAAA,IAAI,GAAG,KAAK,SAAS,EAAE;AACrB,QAAA,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,QAAQ,EAAE,CAAC;;AAGlD,IAAA,OAAO,QAAQ,CAAC,QAAQ,EAAE;AAC5B,CAAC;;ACQD;;;AAGG;AACU,MAAA,OAAO,GAAGA,SAAI,CAAC,MAAM,CAAiB;AACjD,IAAA,IAAI,EAAE,SAAS;IAEf,UAAU,GAAA;QACR,OAAO;AACL,YAAA,eAAe,EAAE,IAAI;AACrB,YAAA,eAAe,EAAE,IAAI;AACrB,YAAA,QAAQ,EAAE,KAAK;AACf,YAAA,UAAU,EAAE,SAAS;AACrB,YAAA,YAAY,EAAE,SAAS;AACvB,YAAA,QAAQ,EAAE,IAAI;AACd,YAAA,iBAAiB,EAAE,KAAK;AACxB,YAAA,eAAe,EAAE,KAAK;AACtB,YAAA,OAAO,EAAE,CAAC;AACV,YAAA,MAAM,EAAE,GAAG;AACX,YAAA,iBAAiB,EAAE,SAAS;AAC5B,YAAA,YAAY,EAAE,CAAC;AACf,YAAA,IAAI,EAAE,KAAK;AACX,YAAA,cAAc,EAAE,KAAK;AACrB,YAAA,cAAc,EAAE,EAAE;AAClB,YAAA,MAAM,EAAE,KAAK;AACb,YAAA,QAAQ,EAAE,KAAK;AACf,YAAA,MAAM,EAAE,EAAE;AACV,YAAA,QAAQ,EAAE,EAAE;AACZ,YAAA,gBAAgB,EAAE,SAAS;AAC3B,YAAA,KAAK,EAAE,GAAG;AACV,YAAA,GAAG,EAAE,CAAC;SACP;KACF;IAED,MAAM,GAAA;AACJ,QAAA,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM;KAC3B;IAED,KAAK,GAAA;AACH,QAAA,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,QAAQ,GAAG,OAAO;KAChD;AAED,IAAA,SAAS,EAAE,IAAI;IAEf,aAAa,GAAA;QACX,OAAO;AACL,YAAA,GAAG,EAAE;AACH,gBAAA,OAAO,EAAE,IAAI;AACd,aAAA;AACD,YAAA,KAAK,EAAE;AACL,gBAAA,OAAO,EAAE,CAAC;AACX,aAAA;AACD,YAAA,KAAK,EAAE;AACL,gBAAA,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK;AAC5B,aAAA;AACD,YAAA,MAAM,EAAE;AACN,gBAAA,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM;AAC7B,aAAA;SACF;KACF;IAED,SAAS,GAAA;QACP,OAAO;AACL,YAAA;AACE,gBAAA,GAAG,EAAE,gCAAgC;AACtC,aAAA;SACF;KACF;IAED,WAAW,GAAA;QACT,OAAO;YACL,eAAe,EAAE,CAAC,OAA+B,KAAK,CAAC,EAAE,QAAQ,EAAE,KAAI;gBACrE,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;AACnC,oBAAA,OAAO,KAAK;;gBAGd,OAAO,QAAQ,CAAC,aAAa,CAAC;oBAC5B,IAAI,EAAE,IAAI,CAAC,IAAI;AACf,oBAAA,KAAK,EAAE,OAAO;AACf,iBAAA,CAAC;aACH;SACF;KACF;IAED,aAAa,GAAA;AACX,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE;AACjC,YAAA,OAAO,EAAE;;QAGX,OAAO;AACL,YAAAC,kBAAa,CAAC;AACZ,gBAAA,IAAI,EAAE,oBAAoB;gBAC1B,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,aAAa,EAAE,KAAK,IAAG;AACrB,oBAAA,OAAO,EAAE,GAAG,EAAE,KAAK,CAAC,KAAK,EAAE;iBAC5B;aACF,CAAC;SACH;KACF;IAED,UAAU,CAAC,EAAE,cAAc,EAAE,EAAA;QAC3B,MAAM,QAAQ,GAAG,yBAAyB,CAAC;YACzC,GAAG,EAAE,cAAc,CAAC,GAAG;AACvB,YAAA,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe;AAC7C,YAAA,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ;AAC/B,YAAA,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU;AACnC,YAAA,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY;AACvC,YAAA,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ;AAC/B,YAAA,iBAAiB,EAAE,IAAI,CAAC,OAAO,CAAC,iBAAiB;AACjD,YAAA,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe;AAC7C,YAAA,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO;AAC7B,YAAA,iBAAiB,EAAE,IAAI,CAAC,OAAO,CAAC,iBAAiB;AACjD,YAAA,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY;AACvC,YAAA,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI;AACvB,YAAA,cAAc,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc;AAC3C,YAAA,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ;AAC/B,YAAA,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM;AAC3B,YAAA,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ;AAC/B,YAAA,gBAAgB,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB;AAC/C,YAAA,OAAO,EAAE,cAAc,CAAC,KAAK,IAAI,CAAC;AAClC,YAAA,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG;AACtB,SAAA,CAAC;AAEF,QAAA,cAAc,CAAC,GAAG,GAAG,QAAQ;QAE7B,OAAO;YACL,KAAK;YACL,EAAE,oBAAoB,EAAE,EAAE,EAAE;AAC5B,YAAA;gBACE,QAAQ;AACR,gBAAAC,oBAAe,CACb,IAAI,CAAC,OAAO,CAAC,cAAc,EAC3B;AACE,oBAAA,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK;AACzB,oBAAA,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM;AAC3B,oBAAA,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe;AAC7C,oBAAA,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ;AAC/B,oBAAA,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU;AACnC,oBAAA,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY;AACvC,oBAAA,iBAAiB,EAAE,IAAI,CAAC,OAAO,CAAC,iBAAiB;AACjD,oBAAA,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe;AAC7C,oBAAA,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO;AAC7B,oBAAA,iBAAiB,EAAE,IAAI,CAAC,OAAO,CAAC,iBAAiB;AACjD,oBAAA,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY;AACvC,oBAAA,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI;AACvB,oBAAA,cAAc,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc;AAC3C,oBAAA,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM;AAC3B,oBAAA,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ;AAC/B,oBAAA,gBAAgB,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB;AAC/C,oBAAA,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG;AACtB,iBAAA,EACD,cAAc,CACf;AACF,aAAA;SACF;KACF;AACF,CAAA;;;;;"}