{"version": 3, "file": "index.umd.js", "sources": ["../src/utils.ts", "../src/youtube.ts"], "sourcesContent": ["export const YOUTUBE_REGEX = /^((?:https?:)?\\/\\/)?((?:www|m|music)\\.)?((?:youtube\\.com|youtu.be|youtube-nocookie\\.com))(\\/(?:[\\w-]+\\?v=|embed\\/|v\\/)?)([\\w-]+)(\\S+)?$/\nexport const YOUTUBE_REGEX_GLOBAL = /^((?:https?:)?\\/\\/)?((?:www|m|music)\\.)?((?:youtube\\.com|youtu.be|youtube-nocookie\\.com))(\\/(?:[\\w-]+\\?v=|embed\\/|v\\/)?)([\\w-]+)(\\S+)?$/g\n\nexport const isValidYoutubeUrl = (url: string) => {\n  return url.match(YOUTUBE_REGEX)\n}\n\nexport interface GetEmbedUrlOptions {\n  url: string;\n  allowFullscreen?: boolean;\n  autoplay?: boolean;\n  ccLanguage?:string;\n  ccLoadPolicy?:boolean;\n  controls?: boolean;\n  disableKBcontrols?: boolean,\n  enableIFrameApi?: boolean;\n  endTime?: number;\n  interfaceLanguage?: string;\n  ivLoadPolicy?: number;\n  loop?: boolean;\n  modestBranding?: boolean;\n  nocookie?: boolean;\n  origin?: string;\n  playlist?: string;\n  progressBarColor?: string;\n  startAt?: number;\n  rel?: number;\n}\n\nexport const getYoutubeEmbedUrl = (nocookie?: boolean, isPlaylist?:boolean) => {\n  if (isPlaylist) {\n    return 'https://www.youtube-nocookie.com/embed/videoseries?list='\n  }\n  return nocookie ? 'https://www.youtube-nocookie.com/embed/' : 'https://www.youtube.com/embed/'\n}\n\nconst getYoutubeVideoOrPlaylistId = (\n  url: URL,\n): { id: string; isPlaylist?: boolean } | null => {\n  if (url.searchParams.has('v')) {\n    return { id: url.searchParams.get('v')! }\n  }\n\n  if (\n    url.hostname === 'youtu.be'\n    || url.pathname.includes('shorts')\n    || url.pathname.includes('live')\n  ) {\n    return { id: url.pathname.split('/').pop()! }\n  }\n\n  if (url.searchParams.has('list')) {\n    return { id: url.searchParams.get('list')!, isPlaylist: true }\n  }\n\n  return null\n}\n\nexport const getEmbedUrlFromYoutubeUrl = (options: GetEmbedUrlOptions) => {\n  const {\n    url,\n    allowFullscreen,\n    autoplay,\n    ccLanguage,\n    ccLoadPolicy,\n    controls,\n    disableKBcontrols,\n    enableIFrameApi,\n    endTime,\n    interfaceLanguage,\n    ivLoadPolicy,\n    loop,\n    modestBranding,\n    nocookie,\n    origin,\n    playlist,\n    progressBarColor,\n    startAt,\n    rel,\n  } = options\n\n  if (!isValidYoutubeUrl(url)) {\n    return null\n  }\n\n  // if is already an embed url, return it\n  if (url.includes('/embed/')) {\n    return url\n  }\n\n  const urlObject = new URL(url)\n  const { id, isPlaylist } = getYoutubeVideoOrPlaylistId(urlObject) ?? {}\n\n  if (!id) { return null }\n\n  const embedUrl = new URL(`${getYoutubeEmbedUrl(nocookie, isPlaylist)}${id}`)\n\n  if (urlObject.searchParams.has('t')) {\n    embedUrl.searchParams.set('start', urlObject.searchParams.get('t')!.replaceAll('s', ''))\n  }\n\n  if (allowFullscreen === false) {\n    embedUrl.searchParams.set('fs', '0')\n  }\n\n  if (autoplay) {\n    embedUrl.searchParams.set('autoplay', '1')\n  }\n\n  if (ccLanguage) {\n    embedUrl.searchParams.set('cc_lang_pref', ccLanguage)\n  }\n\n  if (ccLoadPolicy) {\n    embedUrl.searchParams.set('cc_load_policy', '1')\n  }\n\n  if (!controls) {\n    embedUrl.searchParams.set('controls', '0')\n  }\n\n  if (disableKBcontrols) {\n    embedUrl.searchParams.set('disablekb', '1')\n  }\n\n  if (enableIFrameApi) {\n    embedUrl.searchParams.set('enablejsapi', '1')\n  }\n\n  if (endTime) {\n    embedUrl.searchParams.set('end', endTime.toString())\n  }\n\n  if (interfaceLanguage) {\n    embedUrl.searchParams.set('hl', interfaceLanguage)\n  }\n\n  if (ivLoadPolicy) {\n    embedUrl.searchParams.set('iv_load_policy', ivLoadPolicy.toString())\n  }\n\n  if (loop) {\n    embedUrl.searchParams.set('loop', '1')\n  }\n\n  if (modestBranding) {\n    embedUrl.searchParams.set('modestbranding', '1')\n  }\n\n  if (origin) {\n    embedUrl.searchParams.set('origin', origin)\n  }\n\n  if (playlist) {\n    embedUrl.searchParams.set('playlist', playlist)\n  }\n\n  if (startAt) {\n    embedUrl.searchParams.set('start', startAt.toString())\n  }\n\n  if (progressBarColor) {\n    embedUrl.searchParams.set('color', progressBarColor)\n  }\n\n  if (rel !== undefined) {\n    embedUrl.searchParams.set('rel', rel.toString())\n  }\n\n  return embedUrl.toString()\n}\n", "import { mergeAttributes, Node, nodePasteRule } from '@tiptap/core'\n\nimport { getEmbedUrlFromYoutubeUrl, isValidYoutubeUrl, YOUTUBE_REGEX_GLOBAL } from './utils.js'\n\nexport interface YoutubeOptions {\n  /**\n   * Controls if the paste handler for youtube videos should be added.\n   * @default true\n   * @example false\n   */\n  addPasteHandler: boolean;\n\n  /**\n   * Controls if the youtube video should be allowed to go fullscreen.\n   * @default true\n   * @example false\n   */\n  allowFullscreen: boolean;\n\n  /**\n   * Controls if the youtube video should autoplay.\n   * @default false\n   * @example true\n   */\n  autoplay: boolean;\n\n  /**\n   * The language of the captions shown in the youtube video.\n   * @default undefined\n   * @example 'en'\n   */\n  ccLanguage?: string;\n\n  /**\n   * Controls if the captions should be shown in the youtube video.\n   * @default undefined\n   * @example true\n   */\n  ccLoadPolicy?: boolean;\n\n  /**\n   * Controls if the controls should be shown in the youtube video.\n   * @default true\n   * @example false\n   */\n  controls: boolean;\n\n  /**\n   * Controls if the keyboard controls should be disabled in the youtube video.\n   * @default false\n   * @example true\n   */\n  disableKBcontrols: boolean;\n\n  /**\n   * Controls if the iframe api should be enabled in the youtube video.\n   * @default false\n   * @example true\n   */\n  enableIFrameApi: boolean;\n\n  /**\n   * The end time of the youtube video.\n   * @default 0\n   * @example 120\n   */\n  endTime: number;\n\n  /**\n   * The height of the youtube video.\n   * @default 480\n   * @example 720\n   */\n  height: number;\n\n  /**\n   * The language of the youtube video.\n   * @default undefined\n   * @example 'en'\n   */\n  interfaceLanguage?: string;\n\n  /**\n   * Controls if the video annotations should be shown in the youtube video.\n   * @default 0\n   * @example 1\n   */\n  ivLoadPolicy: number;\n\n  /**\n   * Controls if the youtube video should loop.\n   * @default false\n   * @example true\n   */\n  loop: boolean;\n\n  /**\n   * Controls if the youtube video should show a small youtube logo.\n   * @default false\n   * @example true\n   */\n  modestBranding: boolean;\n\n  /**\n   * The HTML attributes for a youtube video node.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>;\n\n  /**\n   * Controls if the youtube node should be inline or not.\n   * @default false\n   * @example true\n   */\n  inline: boolean;\n\n  /**\n   * Controls if the youtube video should be loaded from youtube-nocookie.com.\n   * @default false\n   * @example true\n   */\n  nocookie: boolean;\n\n  /**\n   * The origin of the youtube video.\n   * @default ''\n   * @example 'https://tiptap.dev'\n   */\n  origin: string;\n\n  /**\n   * The playlist of the youtube video.\n   * @default ''\n   * @example 'PLQg6GaokU5CwiVmsZ0dZm6VeIg0V5z1tK'\n   */\n  playlist: string;\n\n  /**\n   * The color of the youtube video progress bar.\n   * @default undefined\n   * @example 'red'\n   */\n  progressBarColor?: string;\n\n  /**\n   * The width of the youtube video.\n   * @default 640\n   * @example 1280\n   */\n  width: number;\n\n  /**\n   * Controls if the related youtube videos at the end are from the same channel.\n   * @default 1\n   * @example 0\n   */\n  rel: number;\n}\n\n/**\n * The options for setting a youtube video.\n */\ntype SetYoutubeVideoOptions = { src: string, width?: number, height?: number, start?: number }\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    youtube: {\n      /**\n       * Insert a youtube video\n       * @param options The youtube video attributes\n       * @example editor.commands.setYoutubeVideo({ src: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ' })\n       */\n      setYoutubeVideo: (options: SetYoutubeVideoOptions) => ReturnType,\n    }\n  }\n}\n\n/**\n * This extension adds support for youtube videos.\n * @see https://www.tiptap.dev/api/nodes/youtube\n */\nexport const Youtube = Node.create<YoutubeOptions>({\n  name: 'youtube',\n\n  addOptions() {\n    return {\n      addPasteHandler: true,\n      allowFullscreen: true,\n      autoplay: false,\n      ccLanguage: undefined,\n      ccLoadPolicy: undefined,\n      controls: true,\n      disableKBcontrols: false,\n      enableIFrameApi: false,\n      endTime: 0,\n      height: 480,\n      interfaceLanguage: undefined,\n      ivLoadPolicy: 0,\n      loop: false,\n      modestBranding: false,\n      HTMLAttributes: {},\n      inline: false,\n      nocookie: false,\n      origin: '',\n      playlist: '',\n      progressBarColor: undefined,\n      width: 640,\n      rel: 1,\n    }\n  },\n\n  inline() {\n    return this.options.inline\n  },\n\n  group() {\n    return this.options.inline ? 'inline' : 'block'\n  },\n\n  draggable: true,\n\n  addAttributes() {\n    return {\n      src: {\n        default: null,\n      },\n      start: {\n        default: 0,\n      },\n      width: {\n        default: this.options.width,\n      },\n      height: {\n        default: this.options.height,\n      },\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: 'div[data-youtube-video] iframe',\n      },\n    ]\n  },\n\n  addCommands() {\n    return {\n      setYoutubeVideo: (options: SetYoutubeVideoOptions) => ({ commands }) => {\n        if (!isValidYoutubeUrl(options.src)) {\n          return false\n        }\n\n        return commands.insertContent({\n          type: this.name,\n          attrs: options,\n        })\n      },\n    }\n  },\n\n  addPasteRules() {\n    if (!this.options.addPasteHandler) {\n      return []\n    }\n\n    return [\n      nodePasteRule({\n        find: YOUTUBE_REGEX_GLOBAL,\n        type: this.type,\n        getAttributes: match => {\n          return { src: match.input }\n        },\n      }),\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    const embedUrl = getEmbedUrlFromYoutubeUrl({\n      url: HTMLAttributes.src,\n      allowFullscreen: this.options.allowFullscreen,\n      autoplay: this.options.autoplay,\n      ccLanguage: this.options.ccLanguage,\n      ccLoadPolicy: this.options.ccLoadPolicy,\n      controls: this.options.controls,\n      disableKBcontrols: this.options.disableKBcontrols,\n      enableIFrameApi: this.options.enableIFrameApi,\n      endTime: this.options.endTime,\n      interfaceLanguage: this.options.interfaceLanguage,\n      ivLoadPolicy: this.options.ivLoadPolicy,\n      loop: this.options.loop,\n      modestBranding: this.options.modestBranding,\n      nocookie: this.options.nocookie,\n      origin: this.options.origin,\n      playlist: this.options.playlist,\n      progressBarColor: this.options.progressBarColor,\n      startAt: HTMLAttributes.start || 0,\n      rel: this.options.rel,\n    })\n\n    HTMLAttributes.src = embedUrl\n\n    return [\n      'div',\n      { 'data-youtube-video': '' },\n      [\n        'iframe',\n        mergeAttributes(\n          this.options.HTMLAttributes,\n          {\n            width: this.options.width,\n            height: this.options.height,\n            allowfullscreen: this.options.allowFullscreen,\n            autoplay: this.options.autoplay,\n            ccLanguage: this.options.ccLanguage,\n            ccLoadPolicy: this.options.ccLoadPolicy,\n            disableKBcontrols: this.options.disableKBcontrols,\n            enableIFrameApi: this.options.enableIFrameApi,\n            endTime: this.options.endTime,\n            interfaceLanguage: this.options.interfaceLanguage,\n            ivLoadPolicy: this.options.ivLoadPolicy,\n            loop: this.options.loop,\n            modestBranding: this.options.modestBranding,\n            origin: this.options.origin,\n            playlist: this.options.playlist,\n            progressBarColor: this.options.progressBarColor,\n            rel: this.options.rel,\n          },\n          HTMLAttributes,\n        ),\n      ],\n    ]\n  },\n})\n"], "names": ["Node", "nodePasteRule", "mergeAttributes"], "mappings": ";;;;;;EAAO,MAAM,aAAa,GAAG,yIAAyI;EAC/J,MAAM,oBAAoB,GAAG,0IAA0I;EAEvK,MAAM,iBAAiB,GAAG,CAAC,GAAW,KAAI;EAC/C,IAAA,OAAO,GAAG,CAAC,KAAK,CAAC,aAAa,CAAC;EACjC,CAAC;EAwBM,MAAM,kBAAkB,GAAG,CAAC,QAAkB,EAAE,UAAmB,KAAI;MAC5E,IAAI,UAAU,EAAE;EACd,QAAA,OAAO,0DAA0D;;MAEnE,OAAO,QAAQ,GAAG,yCAAyC,GAAG,gCAAgC;EAChG,CAAC;EAED,MAAM,2BAA2B,GAAG,CAClC,GAAQ,KACuC;MAC/C,IAAI,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;EAC7B,QAAA,OAAO,EAAE,EAAE,EAAE,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAE,EAAE;;EAG3C,IAAA,IACE,GAAG,CAAC,QAAQ,KAAK;EACd,WAAA,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ;aAC9B,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,EAChC;EACA,QAAA,OAAO,EAAE,EAAE,EAAE,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAG,EAAE;;MAG/C,IAAI,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;EAChC,QAAA,OAAO,EAAE,EAAE,EAAE,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAE,EAAE,UAAU,EAAE,IAAI,EAAE;;EAGhE,IAAA,OAAO,IAAI;EACb,CAAC;EAEM,MAAM,yBAAyB,GAAG,CAAC,OAA2B,KAAI;;EACvE,IAAA,MAAM,EACJ,GAAG,EACH,eAAe,EACf,QAAQ,EACR,UAAU,EACV,YAAY,EACZ,QAAQ,EACR,iBAAiB,EACjB,eAAe,EACf,OAAO,EACP,iBAAiB,EACjB,YAAY,EACZ,IAAI,EACJ,cAAc,EACd,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,OAAO,EACP,GAAG,GACJ,GAAG,OAAO;EAEX,IAAA,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,EAAE;EAC3B,QAAA,OAAO,IAAI;;;EAIb,IAAA,IAAI,GAAG,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;EAC3B,QAAA,OAAO,GAAG;;EAGZ,IAAA,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC;EAC9B,IAAA,MAAM,EAAE,EAAE,EAAE,UAAU,EAAE,GAAG,CAAA,EAAA,GAAA,2BAA2B,CAAC,SAAS,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,EAAE;MAEvE,IAAI,CAAC,EAAE,EAAE;EAAE,QAAA,OAAO,IAAI;;EAEtB,IAAA,MAAM,QAAQ,GAAG,IAAI,GAAG,CAAC,GAAG,kBAAkB,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAA,EAAG,EAAE,CAAA,CAAE,CAAC;MAE5E,IAAI,SAAS,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;UACnC,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,SAAS,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAE,CAAC,UAAU,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;;EAG1F,IAAA,IAAI,eAAe,KAAK,KAAK,EAAE;UAC7B,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;;MAGtC,IAAI,QAAQ,EAAE;UACZ,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC;;MAG5C,IAAI,UAAU,EAAE;UACd,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,cAAc,EAAE,UAAU,CAAC;;MAGvD,IAAI,YAAY,EAAE;UAChB,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,gBAAgB,EAAE,GAAG,CAAC;;MAGlD,IAAI,CAAC,QAAQ,EAAE;UACb,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC;;MAG5C,IAAI,iBAAiB,EAAE;UACrB,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC;;MAG7C,IAAI,eAAe,EAAE;UACnB,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,aAAa,EAAE,GAAG,CAAC;;MAG/C,IAAI,OAAO,EAAE;EACX,QAAA,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,QAAQ,EAAE,CAAC;;MAGtD,IAAI,iBAAiB,EAAE;UACrB,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,EAAE,iBAAiB,CAAC;;MAGpD,IAAI,YAAY,EAAE;EAChB,QAAA,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,gBAAgB,EAAE,YAAY,CAAC,QAAQ,EAAE,CAAC;;MAGtE,IAAI,IAAI,EAAE;UACR,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC;;MAGxC,IAAI,cAAc,EAAE;UAClB,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,gBAAgB,EAAE,GAAG,CAAC;;MAGlD,IAAI,MAAM,EAAE;UACV,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC;;MAG7C,IAAI,QAAQ,EAAE;UACZ,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC;;MAGjD,IAAI,OAAO,EAAE;EACX,QAAA,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,QAAQ,EAAE,CAAC;;MAGxD,IAAI,gBAAgB,EAAE;UACpB,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,gBAAgB,CAAC;;EAGtD,IAAA,IAAI,GAAG,KAAK,SAAS,EAAE;EACrB,QAAA,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,QAAQ,EAAE,CAAC;;EAGlD,IAAA,OAAO,QAAQ,CAAC,QAAQ,EAAE;EAC5B,CAAC;;ECQD;;;EAGG;AACU,QAAA,OAAO,GAAGA,SAAI,CAAC,MAAM,CAAiB;EACjD,IAAA,IAAI,EAAE,SAAS;MAEf,UAAU,GAAA;UACR,OAAO;EACL,YAAA,eAAe,EAAE,IAAI;EACrB,YAAA,eAAe,EAAE,IAAI;EACrB,YAAA,QAAQ,EAAE,KAAK;EACf,YAAA,UAAU,EAAE,SAAS;EACrB,YAAA,YAAY,EAAE,SAAS;EACvB,YAAA,QAAQ,EAAE,IAAI;EACd,YAAA,iBAAiB,EAAE,KAAK;EACxB,YAAA,eAAe,EAAE,KAAK;EACtB,YAAA,OAAO,EAAE,CAAC;EACV,YAAA,MAAM,EAAE,GAAG;EACX,YAAA,iBAAiB,EAAE,SAAS;EAC5B,YAAA,YAAY,EAAE,CAAC;EACf,YAAA,IAAI,EAAE,KAAK;EACX,YAAA,cAAc,EAAE,KAAK;EACrB,YAAA,cAAc,EAAE,EAAE;EAClB,YAAA,MAAM,EAAE,KAAK;EACb,YAAA,QAAQ,EAAE,KAAK;EACf,YAAA,MAAM,EAAE,EAAE;EACV,YAAA,QAAQ,EAAE,EAAE;EACZ,YAAA,gBAAgB,EAAE,SAAS;EAC3B,YAAA,KAAK,EAAE,GAAG;EACV,YAAA,GAAG,EAAE,CAAC;WACP;OACF;MAED,MAAM,GAAA;EACJ,QAAA,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM;OAC3B;MAED,KAAK,GAAA;EACH,QAAA,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,QAAQ,GAAG,OAAO;OAChD;EAED,IAAA,SAAS,EAAE,IAAI;MAEf,aAAa,GAAA;UACX,OAAO;EACL,YAAA,GAAG,EAAE;EACH,gBAAA,OAAO,EAAE,IAAI;EACd,aAAA;EACD,YAAA,KAAK,EAAE;EACL,gBAAA,OAAO,EAAE,CAAC;EACX,aAAA;EACD,YAAA,KAAK,EAAE;EACL,gBAAA,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK;EAC5B,aAAA;EACD,YAAA,MAAM,EAAE;EACN,gBAAA,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM;EAC7B,aAAA;WACF;OACF;MAED,SAAS,GAAA;UACP,OAAO;EACL,YAAA;EACE,gBAAA,GAAG,EAAE,gCAAgC;EACtC,aAAA;WACF;OACF;MAED,WAAW,GAAA;UACT,OAAO;cACL,eAAe,EAAE,CAAC,OAA+B,KAAK,CAAC,EAAE,QAAQ,EAAE,KAAI;kBACrE,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;EACnC,oBAAA,OAAO,KAAK;;kBAGd,OAAO,QAAQ,CAAC,aAAa,CAAC;sBAC5B,IAAI,EAAE,IAAI,CAAC,IAAI;EACf,oBAAA,KAAK,EAAE,OAAO;EACf,iBAAA,CAAC;eACH;WACF;OACF;MAED,aAAa,GAAA;EACX,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE;EACjC,YAAA,OAAO,EAAE;;UAGX,OAAO;EACL,YAAAC,kBAAa,CAAC;EACZ,gBAAA,IAAI,EAAE,oBAAoB;kBAC1B,IAAI,EAAE,IAAI,CAAC,IAAI;kBACf,aAAa,EAAE,KAAK,IAAG;EACrB,oBAAA,OAAO,EAAE,GAAG,EAAE,KAAK,CAAC,KAAK,EAAE;mBAC5B;eACF,CAAC;WACH;OACF;MAED,UAAU,CAAC,EAAE,cAAc,EAAE,EAAA;UAC3B,MAAM,QAAQ,GAAG,yBAAyB,CAAC;cACzC,GAAG,EAAE,cAAc,CAAC,GAAG;EACvB,YAAA,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe;EAC7C,YAAA,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ;EAC/B,YAAA,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU;EACnC,YAAA,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY;EACvC,YAAA,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ;EAC/B,YAAA,iBAAiB,EAAE,IAAI,CAAC,OAAO,CAAC,iBAAiB;EACjD,YAAA,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe;EAC7C,YAAA,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO;EAC7B,YAAA,iBAAiB,EAAE,IAAI,CAAC,OAAO,CAAC,iBAAiB;EACjD,YAAA,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY;EACvC,YAAA,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI;EACvB,YAAA,cAAc,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc;EAC3C,YAAA,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ;EAC/B,YAAA,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM;EAC3B,YAAA,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ;EAC/B,YAAA,gBAAgB,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB;EAC/C,YAAA,OAAO,EAAE,cAAc,CAAC,KAAK,IAAI,CAAC;EAClC,YAAA,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG;EACtB,SAAA,CAAC;EAEF,QAAA,cAAc,CAAC,GAAG,GAAG,QAAQ;UAE7B,OAAO;cACL,KAAK;cACL,EAAE,oBAAoB,EAAE,EAAE,EAAE;EAC5B,YAAA;kBACE,QAAQ;EACR,gBAAAC,oBAAe,CACb,IAAI,CAAC,OAAO,CAAC,cAAc,EAC3B;EACE,oBAAA,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK;EACzB,oBAAA,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM;EAC3B,oBAAA,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe;EAC7C,oBAAA,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ;EAC/B,oBAAA,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU;EACnC,oBAAA,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY;EACvC,oBAAA,iBAAiB,EAAE,IAAI,CAAC,OAAO,CAAC,iBAAiB;EACjD,oBAAA,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe;EAC7C,oBAAA,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO;EAC7B,oBAAA,iBAAiB,EAAE,IAAI,CAAC,OAAO,CAAC,iBAAiB;EACjD,oBAAA,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY;EACvC,oBAAA,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI;EACvB,oBAAA,cAAc,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc;EAC3C,oBAAA,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM;EAC3B,oBAAA,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ;EAC/B,oBAAA,gBAAgB,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB;EAC/C,oBAAA,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG;EACtB,iBAAA,EACD,cAAc,CACf;EACF,aAAA;WACF;OACF;EACF,CAAA;;;;;;;;;;;"}