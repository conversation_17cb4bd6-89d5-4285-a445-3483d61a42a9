{"version": 3, "file": "link.d.ts", "sourceRoot": "", "sources": ["../src/link.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,IAAI,EACL,MAAM,cAAc,CAAA;AAQrB,MAAM,WAAW,mBAAmB;IAClC;;;;;OAKG;IACH,MAAM,EAAE,MAAM,CAAC;IAEf;;;;OAIG;IACH,eAAe,CAAC,EAAE,OAAO,CAAC;CAC3B;AAED,eAAO,MAAM,UAAU,QAAoI,CAAA;AAE3J;;GAEG;AACH,KAAK,6BAA6B,GAAG,iBAAiB,CAAC;AAEvD,MAAM,WAAW,WAAW;IAC1B;;;;OAIG;IACH,QAAQ,EAAE,OAAO,CAAC;IAElB;;;;OAIG;IACH,SAAS,EAAE,KAAK,CAAC,mBAAmB,GAAG,MAAM,CAAC,CAAC;IAE/C;;;OAGG;IACH,eAAe,EAAE,MAAM,CAAC;IACxB;;;;OAIG;IACH,WAAW,EAAE,OAAO,GAAG,6BAA6B,CAAC;IACrD;;;;OAIG;IACH,WAAW,EAAE,OAAO,CAAC;IAErB;;;;OAIG;IACH,cAAc,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAEpC;;;;;OAKG;IACH,QAAQ,EAAE,CAAC,GAAG,EAAE,MAAM,KAAK,OAAO,CAAC;IAEnC;;;;;;;;;;OAUG;IACH,YAAY,EAAE;IACZ;;OAEG;IACH,GAAG,EAAE,MAAM,EACX,GAAG,EAAE;QACH;;WAEG;QACH,eAAe,EAAE,CAAC,GAAG,EAAE,MAAM,KAAK,OAAO,CAAC;QAC1C;;WAEG;QACH,SAAS,EAAE,KAAK,CAAC,mBAAmB,GAAG,MAAM,CAAC,CAAC;QAC/C;;WAEG;QACH,eAAe,EAAE,MAAM,CAAC;KACzB,KACE,OAAO,CAAC;IAEb;;;;;OAKG;IACH,cAAc,EAAE,CAAC,GAAG,EAAE,MAAM,KAAK,OAAO,CAAC;CAC1C;AAED,OAAO,QAAQ,cAAc,CAAC;IAC5B,UAAU,QAAQ,CAAC,UAAU;QAC3B,IAAI,EAAE;YACJ;;;;eAIG;YACH,OAAO,EAAE,CAAC,UAAU,EAAE;gBACpB,IAAI,EAAE,MAAM,CAAC;gBACb,MAAM,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;gBACvB,GAAG,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;gBACpB,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;aACvB,KAAK,UAAU,CAAC;YACjB;;;;eAIG;YACH,UAAU,EAAE,CAAC,UAAU,EAAE;gBACvB,IAAI,EAAE,MAAM,CAAC;gBACb,MAAM,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;gBACvB,GAAG,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;gBACpB,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;aACvB,KAAK,UAAU,CAAC;YACjB;;;eAGG;YACH,SAAS,EAAE,MAAM,UAAU,CAAC;SAC7B,CAAC;KACH;CACF;AAOD,wBAAgB,YAAY,CAAC,GAAG,EAAE,MAAM,GAAG,SAAS,EAAE,SAAS,CAAC,EAAE,WAAW,CAAC,WAAW,CAAC,kCAoCzF;AAED;;;GAGG;AACH,eAAO,MAAM,IAAI,wBAgPf,CAAA"}