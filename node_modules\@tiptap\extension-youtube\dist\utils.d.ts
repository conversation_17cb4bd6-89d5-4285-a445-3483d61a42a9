export declare const YOUTUBE_REGEX: RegExp;
export declare const YOUTUBE_REGEX_GLOBAL: RegExp;
export declare const isValidYoutubeUrl: (url: string) => RegExpMatchArray | null;
export interface GetEmbedUrlOptions {
    url: string;
    allowFullscreen?: boolean;
    autoplay?: boolean;
    ccLanguage?: string;
    ccLoadPolicy?: boolean;
    controls?: boolean;
    disableKBcontrols?: boolean;
    enableIFrameApi?: boolean;
    endTime?: number;
    interfaceLanguage?: string;
    ivLoadPolicy?: number;
    loop?: boolean;
    modestBranding?: boolean;
    nocookie?: boolean;
    origin?: string;
    playlist?: string;
    progressBarColor?: string;
    startAt?: number;
    rel?: number;
}
export declare const getYoutubeEmbedUrl: (nocookie?: boolean, isPlaylist?: boolean) => "https://www.youtube-nocookie.com/embed/videoseries?list=" | "https://www.youtube-nocookie.com/embed/" | "https://www.youtube.com/embed/";
export declare const getEmbedUrlFromYoutubeUrl: (options: GetEmbedUrlOptions) => string | null;
//# sourceMappingURL=utils.d.ts.map