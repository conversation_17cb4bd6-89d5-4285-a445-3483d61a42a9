{"version": 3, "file": "index.cjs", "sources": ["../src/image.ts"], "sourcesContent": ["import {\n  mergeAttributes,\n  Node,\n  nodeInputRule,\n} from '@tiptap/core'\n\nexport interface ImageOptions {\n  /**\n   * Controls if the image node should be inline or not.\n   * @default false\n   * @example true\n   */\n  inline: boolean,\n\n  /**\n   * Controls if base64 images are allowed. Enable this if you want to allow\n   * base64 image urls in the `src` attribute.\n   * @default false\n   * @example true\n   */\n  allowBase64: boolean,\n\n  /**\n   * HTML attributes to add to the image element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    image: {\n      /**\n       * Add an image\n       * @param options The image attributes\n       * @example\n       * editor\n       *   .commands\n       *   .setImage({ src: 'https://tiptap.dev/logo.png', alt: 'tiptap', title: 'tiptap logo' })\n       */\n      setImage: (options: { src: string, alt?: string, title?: string }) => ReturnType,\n    }\n  }\n}\n\n/**\n * Matches an image to a ![image](src \"title\") on input.\n */\nexport const inputRegex = /(?:^|\\s)(!\\[(.+|:?)]\\((\\S+)(?:(?:\\s+)[\"'](\\S+)[\"'])?\\))$/\n\n/**\n * This extension allows you to insert images.\n * @see https://www.tiptap.dev/api/nodes/image\n */\nexport const Image = Node.create<ImageOptions>({\n  name: 'image',\n\n  addOptions() {\n    return {\n      inline: false,\n      allowBase64: false,\n      HTMLAttributes: {},\n    }\n  },\n\n  inline() {\n    return this.options.inline\n  },\n\n  group() {\n    return this.options.inline ? 'inline' : 'block'\n  },\n\n  draggable: true,\n\n  addAttributes() {\n    return {\n      src: {\n        default: null,\n      },\n      alt: {\n        default: null,\n      },\n      title: {\n        default: null,\n      },\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: this.options.allowBase64\n          ? 'img[src]'\n          : 'img[src]:not([src^=\"data:\"])',\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['img', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes)]\n  },\n\n  addCommands() {\n    return {\n      setImage: options => ({ commands }) => {\n        return commands.insertContent({\n          type: this.name,\n          attrs: options,\n        })\n      },\n    }\n  },\n\n  addInputRules() {\n    return [\n      nodeInputRule({\n        find: inputRegex,\n        type: this.type,\n        getAttributes: match => {\n          const [,, alt, src, title] = match\n\n          return { src, alt, title }\n        },\n      }),\n    ]\n  },\n})\n"], "names": ["Node", "mergeAttributes", "nodeInputRule"], "mappings": ";;;;;;AA8CA;;AAEG;AACI,MAAM,UAAU,GAAG;AAE1B;;;AAGG;AACU,MAAA,KAAK,GAAGA,SAAI,CAAC,MAAM,CAAe;AAC7C,IAAA,IAAI,EAAE,OAAO;IAEb,UAAU,GAAA;QACR,OAAO;AACL,YAAA,MAAM,EAAE,KAAK;AACb,YAAA,WAAW,EAAE,KAAK;AAClB,YAAA,cAAc,EAAE,EAAE;SACnB;KACF;IAED,MAAM,GAAA;AACJ,QAAA,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM;KAC3B;IAED,KAAK,GAAA;AACH,QAAA,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,QAAQ,GAAG,OAAO;KAChD;AAED,IAAA,SAAS,EAAE,IAAI;IAEf,aAAa,GAAA;QACX,OAAO;AACL,YAAA,GAAG,EAAE;AACH,gBAAA,OAAO,EAAE,IAAI;AACd,aAAA;AACD,YAAA,GAAG,EAAE;AACH,gBAAA,OAAO,EAAE,IAAI;AACd,aAAA;AACD,YAAA,KAAK,EAAE;AACL,gBAAA,OAAO,EAAE,IAAI;AACd,aAAA;SACF;KACF;IAED,SAAS,GAAA;QACP,OAAO;AACL,YAAA;AACE,gBAAA,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC;AAChB,sBAAE;AACF,sBAAE,8BAA8B;AACnC,aAAA;SACF;KACF;IAED,UAAU,CAAC,EAAE,cAAc,EAAE,EAAA;AAC3B,QAAA,OAAO,CAAC,KAAK,EAAEC,oBAAe,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;KAC7E;IAED,WAAW,GAAA;QACT,OAAO;YACL,QAAQ,EAAE,OAAO,IAAI,CAAC,EAAE,QAAQ,EAAE,KAAI;gBACpC,OAAO,QAAQ,CAAC,aAAa,CAAC;oBAC5B,IAAI,EAAE,IAAI,CAAC,IAAI;AACf,oBAAA,KAAK,EAAE,OAAO;AACf,iBAAA,CAAC;aACH;SACF;KACF;IAED,aAAa,GAAA;QACX,OAAO;AACL,YAAAC,kBAAa,CAAC;AACZ,gBAAA,IAAI,EAAE,UAAU;gBAChB,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,aAAa,EAAE,KAAK,IAAG;oBACrB,MAAM,KAAI,GAAG,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,KAAK;AAElC,oBAAA,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE;iBAC3B;aACF,CAAC;SACH;KACF;AACF,CAAA;;;;;;"}