/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/about/page";
exports.ids = ["app/about/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fabout%2Fpage&page=%2Fabout%2Fpage&appPaths=%2Fabout%2Fpage&pagePath=private-next-app-dir%2Fabout%2Fpage.tsx&appDir=C%3A%5CUsers%5CUtente%5CDesktop%5CBoilerplate%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUtente%5CDesktop%5CBoilerplate&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fabout%2Fpage&page=%2Fabout%2Fpage&appPaths=%2Fabout%2Fpage&pagePath=private-next-app-dir%2Fabout%2Fpage.tsx&appDir=C%3A%5CUsers%5CUtente%5CDesktop%5CBoilerplate%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUtente%5CDesktop%5CBoilerplate&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/about/page.tsx */ \"(rsc)/./src/app/about/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'about',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/about/page\",\n        pathname: \"/about\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fabout%2Fpage&page=%2Fabout%2Fpage&appPaths=%2Fabout%2Fpage&pagePath=private-next-app-dir%2Fabout%2Fpage.tsx&appDir=C%3A%5CUsers%5CUtente%5CDesktop%5CBoilerplate%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUtente%5CDesktop%5CBoilerplate&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%2C%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5Cheader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%2C%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5Cheader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/image-component.js */ \"(rsc)/./node_modules/next/dist/client/image-component.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/header.tsx */ \"(rsc)/./src/components/layout/header.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1V0ZW50ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNCb2lsZXJwbGF0ZSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDYXBwLWRpciU1QyU1Q2xpbmsuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjIqJTIyJTJDJTIyX19lc01vZHVsZSUyMiUyQyUyMmRlZmF1bHQlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDVXRlbnRlJTVDJTVDRGVza3RvcCU1QyU1Q0JvaWxlcnBsYXRlJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNpbWFnZS1jb21wb25lbnQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDVXRlbnRlJTVDJTVDRGVza3RvcCU1QyU1Q0JvaWxlcnBsYXRlJTVDJTVDc3JjJTVDJTVDY29tcG9uZW50cyU1QyU1Q2xheW91dCU1QyU1Q2hlYWRlci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJIZWFkZXIlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdOQUFnSTtBQUNoSTtBQUNBLHNOQUFrSTtBQUNsSTtBQUNBLGdMQUE2SSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcVXRlbnRlXFxcXERlc2t0b3BcXFxcQm9pbGVycGxhdGVcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcYXBwLWRpclxcXFxsaW5rLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxVdGVudGVcXFxcRGVza3RvcFxcXFxCb2lsZXJwbGF0ZVxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxpbWFnZS1jb21wb25lbnQuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkhlYWRlclwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXFV0ZW50ZVxcXFxEZXNrdG9wXFxcXEJvaWxlcnBsYXRlXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXGxheW91dFxcXFxoZWFkZXIudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%2C%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5Cheader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Csrc%5C%5Ccomponents%5C%5Canalytics%5C%5Cgoogle-analytics.tsx%22%2C%22ids%22%3A%5B%22GoogleAnalytics%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Csrc%5C%5Ccomponents%5C%5Ccookies%5C%5Ccookie-consent-banner.tsx%22%2C%22ids%22%3A%5B%22CookieConsentBanner%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5Csession-provider.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Csrc%5C%5Ccomponents%5C%5Canalytics%5C%5Cgoogle-analytics.tsx%22%2C%22ids%22%3A%5B%22GoogleAnalytics%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Csrc%5C%5Ccomponents%5C%5Ccookies%5C%5Ccookie-consent-banner.tsx%22%2C%22ids%22%3A%5B%22CookieConsentBanner%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5Csession-provider.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/analytics/google-analytics.tsx */ \"(rsc)/./src/components/analytics/google-analytics.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/cookies/cookie-consent-banner.tsx */ \"(rsc)/./src/components/cookies/cookie-consent-banner.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/session-provider.tsx */ \"(rsc)/./src/components/providers/session-provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Csrc%5C%5Ccomponents%5C%5Canalytics%5C%5Cgoogle-analytics.tsx%22%2C%22ids%22%3A%5B%22GoogleAnalytics%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Csrc%5C%5Ccomponents%5C%5Ccookies%5C%5Ccookie-consent-banner.tsx%22%2C%22ids%22%3A%5B%22CookieConsentBanner%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5Csession-provider.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVXRlbnRlXFxEZXNrdG9wXFxCb2lsZXJwbGF0ZVxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/about/page.tsx":
/*!********************************!*\
  !*** ./src/app/about/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AboutPage),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(rsc)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_layout_header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/header */ \"(rsc)/./src/components/layout/header.tsx\");\n/* harmony import */ var _components_layout_footer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout/footer */ \"(rsc)/./src/components/layout/footer.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(rsc)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(rsc)/./src/components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Github_Heart_Linkedin_Mail_Twitter_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Github,Heart,Linkedin,Mail,Twitter,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Github_Heart_Linkedin_Mail_Twitter_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Github,Heart,Linkedin,Mail,Twitter,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Github_Heart_Linkedin_Mail_Twitter_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Github,Heart,Linkedin,Mail,Twitter,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Github_Heart_Linkedin_Mail_Twitter_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Github,Heart,Linkedin,Mail,Twitter,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Github_Heart_Linkedin_Mail_Twitter_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Github,Heart,Linkedin,Mail,Twitter,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Github_Heart_Linkedin_Mail_Twitter_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Github,Heart,Linkedin,Mail,Twitter,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Github_Heart_Linkedin_Mail_Twitter_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Github,Heart,Linkedin,Mail,Twitter,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_Github_Heart_Linkedin_Mail_Twitter_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,Github,Heart,Linkedin,Mail,Twitter,Users!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: 'About',\n    description: 'Learn more about our blog and the team behind it.'\n};\nconst stats = [\n    {\n        icon: _barrel_optimize_names_Award_BookOpen_Github_Heart_Linkedin_Mail_Twitter_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        label: 'Articles Published',\n        value: '150+'\n    },\n    {\n        icon: _barrel_optimize_names_Award_BookOpen_Github_Heart_Linkedin_Mail_Twitter_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        label: 'Monthly Readers',\n        value: '50K+'\n    },\n    {\n        icon: _barrel_optimize_names_Award_BookOpen_Github_Heart_Linkedin_Mail_Twitter_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        label: 'Community Members',\n        value: '5K+'\n    },\n    {\n        icon: _barrel_optimize_names_Award_BookOpen_Github_Heart_Linkedin_Mail_Twitter_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        label: 'Years of Experience',\n        value: '10+'\n    }\n];\nconst team = [\n    {\n        name: 'John Doe',\n        role: 'Founder & Lead Developer',\n        bio: 'Full-stack developer with 8+ years of experience in React, Node.js, and cloud technologies. Passionate about sharing knowledge and building amazing web experiences.',\n        image: '/api/placeholder/200/200',\n        social: {\n            twitter: 'https://twitter.com/johndoe',\n            github: 'https://github.com/johndoe',\n            linkedin: 'https://linkedin.com/in/johndoe'\n        }\n    },\n    {\n        name: 'Jane Smith',\n        role: 'Content Strategist & Designer',\n        bio: 'UI/UX designer and content creator focused on making complex technical concepts accessible to everyone. Loves creating beautiful and functional designs.',\n        image: '/api/placeholder/200/200',\n        social: {\n            twitter: 'https://twitter.com/janesmith',\n            github: 'https://github.com/janesmith',\n            linkedin: 'https://linkedin.com/in/janesmith'\n        }\n    },\n    {\n        name: 'Mike Johnson',\n        role: 'Technical Writer',\n        bio: 'Former software engineer turned technical writer. Specializes in breaking down complex development topics into easy-to-understand tutorials and guides.',\n        image: '/api/placeholder/200/200',\n        social: {\n            twitter: 'https://twitter.com/mikejohnson',\n            github: 'https://github.com/mikejohnson',\n            linkedin: 'https://linkedin.com/in/mikejohnson'\n        }\n    }\n];\nfunction AboutPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_header__WEBPACK_IMPORTED_MODULE_3__.Header, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"bg-gradient-to-br from-primary/10 via-background to-secondary/10 py-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-4xl mx-auto text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-4xl md:text-6xl font-bold mb-6\",\n                                        children: \"About Our Blog\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-muted-foreground mb-8 max-w-3xl mx-auto\",\n                                        children: \"We're passionate developers and designers sharing our knowledge, experiences, and insights about modern web development, design trends, and technology innovations.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-4xl mx-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid md:grid-cols-2 gap-12 items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-3xl font-bold mb-6\",\n                                                    children: \"Our Mission\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted-foreground mb-6\",\n                                                    children: \"We believe that knowledge should be accessible to everyone. Our mission is to create high-quality, practical content that helps developers and designers grow their skills and build amazing things.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 104,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted-foreground mb-6\",\n                                                    children: \"Whether you're just starting your journey in web development or you're a seasoned professional looking to stay updated with the latest trends, we're here to support your learning journey.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                href: \"/blog\",\n                                                                children: \"Explore Articles\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                                lineNumber: 116,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                            lineNumber: 115,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                            variant: \"outline\",\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                href: \"/contact\",\n                                                                children: \"Get in Touch\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                                lineNumber: 121,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                            lineNumber: 120,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 114,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"aspect-square bg-gradient-to-br from-primary/20 to-secondary/20 rounded-2xl p-8\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full h-full bg-background rounded-xl shadow-lg flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Github_Heart_Linkedin_Mail_Twitter_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                className: \"h-16 w-16 mx-auto mb-4 text-primary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                                lineNumber: 131,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-xl font-semibold mb-2\",\n                                                                children: \"Knowledge Sharing\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                                lineNumber: 132,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-muted-foreground\",\n                                                                children: \"Empowering developers through education\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                                lineNumber: 133,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20 bg-muted/50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-4xl mx-auto text-center mb-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-3xl font-bold mb-4\",\n                                            children: \"Our Impact\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-muted-foreground\",\n                                            children: \"We're proud of the community we've built and the impact we've made\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid md:grid-cols-4 gap-8\",\n                                    children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                                className: \"p-6 text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                                        className: \"h-8 w-8 mx-auto mb-4 text-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 159,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl font-bold mb-2\",\n                                                        children: stat.value\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-muted-foreground\",\n                                                        children: stat.label\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 161,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-4xl mx-auto text-center mb-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-3xl font-bold mb-4\",\n                                            children: \"Meet Our Team\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-muted-foreground\",\n                                            children: \"The passionate individuals behind our content\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid md:grid-cols-3 gap-8\",\n                                    children: team.map((member, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                                className: \"p-6 text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative w-32 h-32 mx-auto mb-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                            src: member.image,\n                                                            alt: member.name,\n                                                            fill: true,\n                                                            className: \"object-cover rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                            lineNumber: 184,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold mb-1\",\n                                                        children: member.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-primary font-medium mb-3\",\n                                                        children: member.role\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-muted-foreground text-sm mb-4\",\n                                                        children: member.bio\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                variant: \"ghost\",\n                                                                size: \"sm\",\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: member.social.twitter,\n                                                                    target: \"_blank\",\n                                                                    rel: \"noopener noreferrer\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Github_Heart_Linkedin_Mail_Twitter_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                                        lineNumber: 199,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                                    lineNumber: 198,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                                lineNumber: 197,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                variant: \"ghost\",\n                                                                size: \"sm\",\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: member.social.github,\n                                                                    target: \"_blank\",\n                                                                    rel: \"noopener noreferrer\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Github_Heart_Linkedin_Mail_Twitter_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                                        lineNumber: 204,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                                    lineNumber: 203,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                                lineNumber: 202,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                variant: \"ghost\",\n                                                                size: \"sm\",\n                                                                asChild: true,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: member.social.linkedin,\n                                                                    target: \"_blank\",\n                                                                    rel: \"noopener noreferrer\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Github_Heart_Linkedin_Mail_Twitter_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                                        lineNumber: 209,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                                    lineNumber: 208,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                                lineNumber: 207,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20 bg-muted/50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-4xl mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mb-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold mb-4\",\n                                                children: \"Our Values\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-muted-foreground\",\n                                                children: \"The principles that guide everything we do\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid md:grid-cols-2 gap-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-xl font-semibold mb-2\",\n                                                                children: \"Quality First\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                                lineNumber: 234,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-muted-foreground\",\n                                                                children: \"We prioritize quality over quantity, ensuring every piece of content provides real value to our readers.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                                lineNumber: 235,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-xl font-semibold mb-2\",\n                                                                children: \"Community Focused\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                                lineNumber: 242,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-muted-foreground\",\n                                                                children: \"We believe in building a supportive community where developers can learn, share, and grow together.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                                lineNumber: 243,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-xl font-semibold mb-2\",\n                                                                children: \"Always Learning\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                                lineNumber: 252,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-muted-foreground\",\n                                                                children: \"Technology evolves rapidly, and we're committed to staying current and sharing the latest insights with our community.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                                lineNumber: 253,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 251,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-xl font-semibold mb-2\",\n                                                                children: \"Open Source\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                                lineNumber: 260,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-muted-foreground\",\n                                                                children: \"We contribute to and support the open source community that has given us so much.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                                lineNumber: 261,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                            lineNumber: 222,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-4xl mx-auto text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl font-bold mb-4\",\n                                        children: \"Join Our Community\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-muted-foreground mb-8\",\n                                        children: \"Stay updated with our latest articles and connect with fellow developers\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                size: \"lg\",\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/blog\",\n                                                    children: \"Start Reading\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                variant: \"outline\",\n                                                size: \"lg\",\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"mailto:<EMAIL>\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_Github_Heart_Linkedin_Mail_Twitter_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                            lineNumber: 289,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Contact Us\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_footer__WEBPACK_IMPORTED_MODULE_4__.Footer, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n                lineNumber: 299,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\about\\\\page.tsx\",\n        lineNumber: 77,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/about/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"1a4414363de0\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFV0ZW50ZVxcRGVza3RvcFxcQm9pbGVycGxhdGVcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjFhNDQxNDM2M2RlMFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/config */ \"(rsc)/./src/lib/config.ts\");\n/* harmony import */ var _components_analytics_google_analytics__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/analytics/google-analytics */ \"(rsc)/./src/components/analytics/google-analytics.tsx\");\n/* harmony import */ var _components_cookies_cookie_consent_banner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/cookies/cookie-consent-banner */ \"(rsc)/./src/components/cookies/cookie-consent-banner.tsx\");\n/* harmony import */ var _components_providers_session_provider__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/providers/session-provider */ \"(rsc)/./src/components/providers/session-provider.tsx\");\n\n\n\n\n\n\n\nconst metadata = {\n    title: {\n        default: _lib_config__WEBPACK_IMPORTED_MODULE_2__.siteConfig.name,\n        template: `%s | ${_lib_config__WEBPACK_IMPORTED_MODULE_2__.siteConfig.name}`\n    },\n    description: _lib_config__WEBPACK_IMPORTED_MODULE_2__.siteConfig.description,\n    keywords: [\n        'blog',\n        'nextjs',\n        'react',\n        'typescript',\n        'tailwindcss'\n    ],\n    authors: [\n        {\n            name: _lib_config__WEBPACK_IMPORTED_MODULE_2__.siteConfig.name\n        }\n    ],\n    creator: _lib_config__WEBPACK_IMPORTED_MODULE_2__.siteConfig.name,\n    openGraph: {\n        type: 'website',\n        locale: 'en_US',\n        url: _lib_config__WEBPACK_IMPORTED_MODULE_2__.siteConfig.url,\n        title: _lib_config__WEBPACK_IMPORTED_MODULE_2__.siteConfig.name,\n        description: _lib_config__WEBPACK_IMPORTED_MODULE_2__.siteConfig.description,\n        siteName: _lib_config__WEBPACK_IMPORTED_MODULE_2__.siteConfig.name,\n        images: [\n            {\n                url: _lib_config__WEBPACK_IMPORTED_MODULE_2__.siteConfig.ogImage || '/og-image.jpg',\n                width: 1200,\n                height: 630,\n                alt: _lib_config__WEBPACK_IMPORTED_MODULE_2__.siteConfig.name\n            }\n        ]\n    },\n    twitter: {\n        card: 'summary_large_image',\n        title: _lib_config__WEBPACK_IMPORTED_MODULE_2__.siteConfig.name,\n        description: _lib_config__WEBPACK_IMPORTED_MODULE_2__.siteConfig.description,\n        images: [\n            _lib_config__WEBPACK_IMPORTED_MODULE_2__.siteConfig.ogImage || '/og-image.jpg'\n        ],\n        creator: '@yourusername'\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            'max-video-preview': -1,\n            'max-image-preview': 'large',\n            'max-snippet': -1\n        }\n    },\n    manifest: '/manifest.json',\n    icons: {\n        icon: '/favicon.ico',\n        shortcut: '/favicon-16x16.png',\n        apple: '/apple-touch-icon.png'\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_session_provider__WEBPACK_IMPORTED_MODULE_5__.Providers, {\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_analytics_google_analytics__WEBPACK_IMPORTED_MODULE_3__.GoogleAnalytics, {\n                        measurementId: \"G-XXXXXXXXXX\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_cookies_cookie_consent_banner__WEBPACK_IMPORTED_MODULE_4__.CookieConsentBanner, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 70,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 69,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 68,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/analytics/google-analytics.tsx":
/*!*******************************************************!*\
  !*** ./src/components/analytics/google-analytics.tsx ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   GoogleAnalytics: () => (/* binding */ GoogleAnalytics),
/* harmony export */   useAnalytics: () => (/* binding */ useAnalytics)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const GoogleAnalytics = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call GoogleAnalytics() from the server but GoogleAnalytics is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Boilerplate\\src\\components\\analytics\\google-analytics.tsx",
"GoogleAnalytics",
);const useAnalytics = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAnalytics() from the server but useAnalytics is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Boilerplate\\src\\components\\analytics\\google-analytics.tsx",
"useAnalytics",
);

/***/ }),

/***/ "(rsc)/./src/components/cookies/cookie-consent-banner.tsx":
/*!**********************************************************!*\
  !*** ./src/components/cookies/cookie-consent-banner.tsx ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CookieConsentBanner: () => (/* binding */ CookieConsentBanner),
/* harmony export */   CookieSettings: () => (/* binding */ CookieSettings)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const CookieConsentBanner = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call CookieConsentBanner() from the server but CookieConsentBanner is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Boilerplate\\src\\components\\cookies\\cookie-consent-banner.tsx",
"CookieConsentBanner",
);const CookieSettings = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call CookieSettings() from the server but CookieSettings is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Boilerplate\\src\\components\\cookies\\cookie-consent-banner.tsx",
"CookieSettings",
);

/***/ }),

/***/ "(rsc)/./src/components/layout/footer.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Footer: () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Github_Instagram_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Instagram,Twitter!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_Github_Instagram_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Instagram,Twitter!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_Github_Instagram_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Instagram,Twitter!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/instagram.js\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/config */ \"(rsc)/./src/lib/config.ts\");\n\n\n\n\nfunction Footer({ categories = [] }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"border-t bg-background\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold\",\n                                    children: _lib_config__WEBPACK_IMPORTED_MODULE_2__.siteConfig.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                    lineNumber: 19,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: _lib_config__WEBPACK_IMPORTED_MODULE_2__.siteConfig.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                    lineNumber: 20,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-4\",\n                                    children: [\n                                        _lib_config__WEBPACK_IMPORTED_MODULE_2__.siteConfig.links.twitter && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: _lib_config__WEBPACK_IMPORTED_MODULE_2__.siteConfig.links.twitter,\n                                            className: \"text-muted-foreground hover:text-primary\",\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Instagram_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                                lineNumber: 31,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                            lineNumber: 25,\n                                            columnNumber: 17\n                                        }, this),\n                                        _lib_config__WEBPACK_IMPORTED_MODULE_2__.siteConfig.links.github && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: _lib_config__WEBPACK_IMPORTED_MODULE_2__.siteConfig.links.github,\n                                            className: \"text-muted-foreground hover:text-primary\",\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Instagram_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                                lineNumber: 41,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                            lineNumber: 35,\n                                            columnNumber: 17\n                                        }, this),\n                                        _lib_config__WEBPACK_IMPORTED_MODULE_2__.siteConfig.links.instagram && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: _lib_config__WEBPACK_IMPORTED_MODULE_2__.siteConfig.links.instagram,\n                                            className: \"text-muted-foreground hover:text-primary\",\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Instagram_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                                lineNumber: 51,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                            lineNumber: 45,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                    lineNumber: 23,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold\",\n                                    children: \"Quick Links\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/\",\n                                                className: \"text-muted-foreground hover:text-primary\",\n                                                children: \"Home\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                                lineNumber: 62,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/blog\",\n                                                className: \"text-muted-foreground hover:text-primary\",\n                                                children: \"Blog\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/about\",\n                                                className: \"text-muted-foreground hover:text-primary\",\n                                                children: \"About\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                                lineNumber: 78,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/contact\",\n                                                className: \"text-muted-foreground hover:text-primary\",\n                                                children: \"Contact\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                                lineNumber: 86,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold\",\n                                    children: \"Categories\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2 text-sm\",\n                                    children: categories.slice(0, 5).map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: `/blog/category/${category.slug}`,\n                                                className: \"text-muted-foreground hover:text-primary\",\n                                                children: category.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, category.slug, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold\",\n                                    children: \"Newsletter\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: \"Subscribe to get the latest posts delivered to your inbox.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            placeholder: \"Enter your email\",\n                                            className: \"w-full px-3 py-2 text-sm border border-input rounded-md bg-background\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"w-full px-3 py-2 text-sm bg-primary text-primary-foreground rounded-md hover:bg-primary/90\",\n                                            children: \"Subscribe\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 pt-8 border-t text-center text-sm text-muted-foreground\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            \"\\xa9 \",\n                            new Date().getFullYear(),\n                            \" \",\n                            _lib_config__WEBPACK_IMPORTED_MODULE_2__.siteConfig.name,\n                            \". All rights reserved.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\footer.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/layout/footer.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/layout/header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Header: () => (/* binding */ Header)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Header = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Header() from the server but Header is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Boilerplate\\src\\components\\layout\\header.tsx",
"Header",
);

/***/ }),

/***/ "(rsc)/./src/components/providers/session-provider.tsx":
/*!*******************************************************!*\
  !*** ./src/components/providers/session-provider.tsx ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ Providers)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Providers = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Boilerplate\\src\\components\\providers\\session-provider.tsx",
"Providers",
);

/***/ }),

/***/ "(rsc)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(rsc)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./src/lib/utils.ts\");\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 43,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./src/lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 8,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 23,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 31,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 46,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 58,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 66,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(rsc)/./src/lib/config.ts":
/*!***************************!*\
  !*** ./src/lib/config.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ALLOWED_IMAGE_TYPES: () => (/* binding */ ALLOWED_IMAGE_TYPES),\n/* harmony export */   ALLOWED_VIDEO_TYPES: () => (/* binding */ ALLOWED_VIDEO_TYPES),\n/* harmony export */   MAX_FILE_SIZE: () => (/* binding */ MAX_FILE_SIZE),\n/* harmony export */   POSTS_PER_PAGE: () => (/* binding */ POSTS_PER_PAGE),\n/* harmony export */   defaultTheme: () => (/* binding */ defaultTheme),\n/* harmony export */   siteConfig: () => (/* binding */ siteConfig)\n/* harmony export */ });\nconst siteConfig = {\n    name: \"My Blog\" || 0,\n    description: \"A modern blog built with Next.js\" || 0,\n    url: \"http://localhost:3000\" || 0,\n    ogImage: '/og-image.jpg',\n    links: {\n        twitter: 'https://twitter.com/yourusername',\n        github: 'https://github.com/yourusername',\n        instagram: 'https://instagram.com/yourusername'\n    }\n};\nconst defaultTheme = {\n    colors: {\n        primary: '#3B82F6',\n        secondary: '#64748B',\n        accent: '#F59E0B',\n        background: '#FFFFFF',\n        foreground: '#0F172A'\n    },\n    fonts: {\n        heading: 'Inter, sans-serif',\n        body: 'Inter, sans-serif'\n    },\n    spacing: {\n        container: '1200px'\n    }\n};\nconst POSTS_PER_PAGE = 10;\nconst MAX_FILE_SIZE = parseInt(\"5242880\" || 0) // 5MB\n;\nconst ALLOWED_IMAGE_TYPES = [\n    'image/jpeg',\n    'image/png',\n    'image/webp',\n    'image/gif'\n];\nconst ALLOWED_VIDEO_TYPES = [\n    'video/mp4',\n    'video/webm',\n    'video/ogg'\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/config.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   extractTextFromHtml: () => (/* binding */ extractTextFromHtml),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatFileSize: () => (/* binding */ formatFileSize),\n/* harmony export */   formatRelativeTime: () => (/* binding */ formatRelativeTime),\n/* harmony export */   generateExcerpt: () => (/* binding */ generateExcerpt),\n/* harmony export */   generateRandomString: () => (/* binding */ generateRandomString),\n/* harmony export */   getFileExtension: () => (/* binding */ getFileExtension),\n/* harmony export */   isImageFile: () => (/* binding */ isImageFile),\n/* harmony export */   isValidUrl: () => (/* binding */ isValidUrl),\n/* harmony export */   isVideoFile: () => (/* binding */ isVideoFile),\n/* harmony export */   slugify: () => (/* binding */ slugify),\n/* harmony export */   truncateText: () => (/* binding */ truncateText)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(rsc)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(rsc)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction slugify(text) {\n    return text.toLowerCase().replace(/[^\\w\\s-]/g, '') // Remove special characters\n    .replace(/[\\s_-]+/g, '-') // Replace spaces and underscores with hyphens\n    .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens\n    ;\n}\nfunction formatDate(date) {\n    const d = new Date(date);\n    return d.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric'\n    });\n}\nfunction formatRelativeTime(date) {\n    const d = new Date(date);\n    const now = new Date();\n    const diffInSeconds = Math.floor((now.getTime() - d.getTime()) / 1000);\n    if (diffInSeconds < 60) return 'just now';\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;\n    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;\n    if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} days ago`;\n    return formatDate(d);\n}\nfunction truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.slice(0, maxLength).trim() + '...';\n}\nfunction extractTextFromHtml(html) {\n    return html.replace(/<[^>]*>/g, '').trim();\n}\nfunction generateExcerpt(content, maxLength = 160) {\n    const text = extractTextFromHtml(content);\n    return truncateText(text, maxLength);\n}\nfunction isValidUrl(url) {\n    try {\n        new URL(url);\n        return true;\n    } catch  {\n        return false;\n    }\n}\nfunction getFileExtension(filename) {\n    return filename.split('.').pop()?.toLowerCase() || '';\n}\nfunction formatFileSize(bytes) {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = [\n        'Bytes',\n        'KB',\n        'MB',\n        'GB'\n    ];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n}\nfunction isImageFile(filename) {\n    const imageExtensions = [\n        'jpg',\n        'jpeg',\n        'png',\n        'gif',\n        'webp',\n        'svg'\n    ];\n    const extension = getFileExtension(filename);\n    return imageExtensions.includes(extension);\n}\nfunction isVideoFile(filename) {\n    const videoExtensions = [\n        'mp4',\n        'webm',\n        'ogg',\n        'avi',\n        'mov'\n    ];\n    const extension = getFileExtension(filename);\n    return videoExtensions.includes(extension);\n}\nfunction generateRandomString(length) {\n    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';\n    let result = '';\n    for(let i = 0; i < length; i++){\n        result += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    return result;\n}\nfunction debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%2C%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5Cheader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%2C%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5Cheader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/image-component.js */ \"(ssr)/./node_modules/next/dist/client/image-component.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/header.tsx */ \"(ssr)/./src/components/layout/header.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1V0ZW50ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNCb2lsZXJwbGF0ZSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDYXBwLWRpciU1QyU1Q2xpbmsuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjIqJTIyJTJDJTIyX19lc01vZHVsZSUyMiUyQyUyMmRlZmF1bHQlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDVXRlbnRlJTVDJTVDRGVza3RvcCU1QyU1Q0JvaWxlcnBsYXRlJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNpbWFnZS1jb21wb25lbnQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDVXRlbnRlJTVDJTVDRGVza3RvcCU1QyU1Q0JvaWxlcnBsYXRlJTVDJTVDc3JjJTVDJTVDY29tcG9uZW50cyU1QyU1Q2xheW91dCU1QyU1Q2hlYWRlci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJIZWFkZXIlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdOQUFnSTtBQUNoSTtBQUNBLHNOQUFrSTtBQUNsSTtBQUNBLGdMQUE2SSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcVXRlbnRlXFxcXERlc2t0b3BcXFxcQm9pbGVycGxhdGVcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcYXBwLWRpclxcXFxsaW5rLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxVdGVudGVcXFxcRGVza3RvcFxcXFxCb2lsZXJwbGF0ZVxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxpbWFnZS1jb21wb25lbnQuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkhlYWRlclwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXFV0ZW50ZVxcXFxEZXNrdG9wXFxcXEJvaWxlcnBsYXRlXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXGxheW91dFxcXFxoZWFkZXIudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%2C%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cimage-component.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5Cheader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Csrc%5C%5Ccomponents%5C%5Canalytics%5C%5Cgoogle-analytics.tsx%22%2C%22ids%22%3A%5B%22GoogleAnalytics%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Csrc%5C%5Ccomponents%5C%5Ccookies%5C%5Ccookie-consent-banner.tsx%22%2C%22ids%22%3A%5B%22CookieConsentBanner%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5Csession-provider.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Csrc%5C%5Ccomponents%5C%5Canalytics%5C%5Cgoogle-analytics.tsx%22%2C%22ids%22%3A%5B%22GoogleAnalytics%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Csrc%5C%5Ccomponents%5C%5Ccookies%5C%5Ccookie-consent-banner.tsx%22%2C%22ids%22%3A%5B%22CookieConsentBanner%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5Csession-provider.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/analytics/google-analytics.tsx */ \"(ssr)/./src/components/analytics/google-analytics.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/cookies/cookie-consent-banner.tsx */ \"(ssr)/./src/components/cookies/cookie-consent-banner.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/session-provider.tsx */ \"(ssr)/./src/components/providers/session-provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Csrc%5C%5Ccomponents%5C%5Canalytics%5C%5Cgoogle-analytics.tsx%22%2C%22ids%22%3A%5B%22GoogleAnalytics%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Csrc%5C%5Ccomponents%5C%5Ccookies%5C%5Ccookie-consent-banner.tsx%22%2C%22ids%22%3A%5B%22CookieConsentBanner%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUtente%5C%5CDesktop%5C%5CBoilerplate%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5Csession-provider.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/analytics/google-analytics.tsx":
/*!*******************************************************!*\
  !*** ./src/components/analytics/google-analytics.tsx ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GoogleAnalytics: () => (/* binding */ GoogleAnalytics),\n/* harmony export */   useAnalytics: () => (/* binding */ useAnalytics)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/script */ \"(ssr)/./node_modules/next/dist/api/script.js\");\n/* harmony import */ var _lib_analytics__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/analytics */ \"(ssr)/./src/lib/analytics.ts\");\n/* __next_internal_client_entry_do_not_use__ GoogleAnalytics,useAnalytics auto */ \n\n\n\n\nfunction GoogleAnalytics({ measurementId }) {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"GoogleAnalytics.useEffect\": ()=>{\n            // Initialize consent mode first\n            (0,_lib_analytics__WEBPACK_IMPORTED_MODULE_4__.initConsentMode)();\n        }\n    }[\"GoogleAnalytics.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"GoogleAnalytics.useEffect\": ()=>{\n            if (!measurementId) return;\n            // Check if analytics is enabled by user consent\n            if ((0,_lib_analytics__WEBPACK_IMPORTED_MODULE_4__.isAnalyticsEnabled)()) {\n                (0,_lib_analytics__WEBPACK_IMPORTED_MODULE_4__.initGA)(measurementId);\n            }\n        }\n    }[\"GoogleAnalytics.useEffect\"], [\n        measurementId\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"GoogleAnalytics.useEffect\": ()=>{\n            if (!measurementId || !(0,_lib_analytics__WEBPACK_IMPORTED_MODULE_4__.isAnalyticsEnabled)()) return;\n            const url = pathname + searchParams.toString();\n            (0,_lib_analytics__WEBPACK_IMPORTED_MODULE_4__.trackPageView)(url);\n        }\n    }[\"GoogleAnalytics.useEffect\"], [\n        pathname,\n        searchParams,\n        measurementId\n    ]);\n    if (!measurementId || !(0,_lib_analytics__WEBPACK_IMPORTED_MODULE_4__.isAnalyticsEnabled)()) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                src: `https://www.googletagmanager.com/gtag/js?id=${measurementId}`,\n                strategy: \"afterInteractive\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\analytics\\\\google-analytics.tsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                id: \"google-analytics\",\n                strategy: \"afterInteractive\",\n                children: `\n          window.dataLayer = window.dataLayer || [];\n          function gtag(){dataLayer.push(arguments);}\n          gtag('js', new Date());\n          gtag('config', '${measurementId}', {\n            page_title: document.title,\n            page_location: window.location.href,\n          });\n        `\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\analytics\\\\google-analytics.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n// Hook for tracking events in components\nfunction useAnalytics() {\n    return {\n        trackEvent: (action, category, label, value)=>{\n            if (!(0,_lib_analytics__WEBPACK_IMPORTED_MODULE_4__.isAnalyticsEnabled)()) return;\n            if (false) {}\n        },\n        trackPageView: (url, title)=>{\n            if (!(0,_lib_analytics__WEBPACK_IMPORTED_MODULE_4__.isAnalyticsEnabled)()) return;\n            (0,_lib_analytics__WEBPACK_IMPORTED_MODULE_4__.trackPageView)(url, title);\n        },\n        isEnabled: (0,_lib_analytics__WEBPACK_IMPORTED_MODULE_4__.isAnalyticsEnabled)()\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/analytics/google-analytics.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/cookies/cookie-consent-banner.tsx":
/*!**********************************************************!*\
  !*** ./src/components/cookies/cookie-consent-banner.tsx ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CookieConsentBanner: () => (/* binding */ CookieConsentBanner),\n/* harmony export */   CookieSettings: () => (/* binding */ CookieSettings)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_Cookie_Settings_Shield_Target_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronUp,Cookie,Settings,Shield,Target!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_Cookie_Settings_Shield_Target_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronUp,Cookie,Settings,Shield,Target!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_Cookie_Settings_Shield_Target_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronUp,Cookie,Settings,Shield,Target!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_Cookie_Settings_Shield_Target_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronUp,Cookie,Settings,Shield,Target!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/cookie.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_Cookie_Settings_Shield_Target_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronUp,Cookie,Settings,Shield,Target!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_Cookie_Settings_Shield_Target_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronUp,Cookie,Settings,Shield,Target!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_Cookie_Settings_Shield_Target_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronUp,Cookie,Settings,Shield,Target!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _lib_cookies__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/cookies */ \"(ssr)/./src/lib/cookies.ts\");\n/* __next_internal_client_entry_do_not_use__ CookieConsentBanner,CookieSettings auto */ \n\n\n\n\n\n\n\nfunction CookieConsentBanner() {\n    const [showBanner, setShowBanner] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showDetails, setShowDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [preferences, setPreferences] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        necessary: true,\n        analytics: false,\n        marketing: false\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CookieConsentBanner.useEffect\": ()=>{\n            // Check if banner should be shown\n            setShowBanner((0,_lib_cookies__WEBPACK_IMPORTED_MODULE_6__.shouldShowConsentBanner)());\n            // Load existing preferences if any\n            const existingConsent = (0,_lib_cookies__WEBPACK_IMPORTED_MODULE_6__.getConsentFromStorage)();\n            if (existingConsent) {\n                setPreferences({\n                    necessary: existingConsent.necessary,\n                    analytics: existingConsent.analytics,\n                    marketing: existingConsent.marketing\n                });\n            }\n        }\n    }[\"CookieConsentBanner.useEffect\"], []);\n    const handleAcceptAll = ()=>{\n        (0,_lib_cookies__WEBPACK_IMPORTED_MODULE_6__.acceptAllCookies)();\n        setShowBanner(false);\n        // Update Google Consent Mode\n        (0,_lib_cookies__WEBPACK_IMPORTED_MODULE_6__.updateConsentMode)({\n            necessary: true,\n            analytics: true,\n            marketing: true,\n            timestamp: Date.now(),\n            version: '1.0'\n        });\n    };\n    const handleAcceptNecessary = ()=>{\n        (0,_lib_cookies__WEBPACK_IMPORTED_MODULE_6__.acceptNecessaryCookies)();\n        setShowBanner(false);\n        // Update Google Consent Mode\n        (0,_lib_cookies__WEBPACK_IMPORTED_MODULE_6__.updateConsentMode)({\n            necessary: true,\n            analytics: false,\n            marketing: false,\n            timestamp: Date.now(),\n            version: '1.0'\n        });\n    };\n    const handleSavePreferences = ()=>{\n        (0,_lib_cookies__WEBPACK_IMPORTED_MODULE_6__.updateConsent)(preferences);\n        setShowBanner(false);\n        // Update Google Consent Mode\n        (0,_lib_cookies__WEBPACK_IMPORTED_MODULE_6__.updateConsentMode)({\n            ...preferences,\n            timestamp: Date.now(),\n            version: '1.0'\n        });\n    };\n    const handlePreferenceChange = (category, value)=>{\n        if (category === 'necessary') return; // Can't disable necessary cookies\n        setPreferences((prev)=>({\n                ...prev,\n                [category]: value\n            }));\n    };\n    const getCategoryIcon = (category)=>{\n        switch(category){\n            case 'necessary':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_Cookie_Settings_Shield_Target_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n                    lineNumber: 105,\n                    columnNumber: 16\n                }, this);\n            case 'analytics':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_Cookie_Settings_Shield_Target_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 16\n                }, this);\n            case 'marketing':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_Cookie_Settings_Shield_Target_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_Cookie_Settings_Shield_Target_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    if (!showBanner) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-0 left-0 right-0 z-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n            className: \"mx-auto max-w-4xl border-2 shadow-lg\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                className: \"p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_Cookie_Settings_Shield_Target_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-6 w-6 text-primary\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-2\",\n                                            children: \"We use cookies to enhance your experience\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: [\n                                                \"We use cookies to analyze our traffic, personalize content and ads, and provide social media features. You can choose which cookies to accept below.\",\n                                                ' ',\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/cookie-policy\",\n                                                    className: \"text-primary hover:underline\",\n                                                    children: \"Learn more about our cookie policy\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 15\n                                }, this),\n                                showDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4 border-t pt-4\",\n                                    children: Object.entries(_lib_cookies__WEBPACK_IMPORTED_MODULE_6__.cookieCategories).map(([key, category])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start justify-between space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 mb-1\",\n                                                            children: [\n                                                                getCategoryIcon(key),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: category.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n                                                                    lineNumber: 148,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                !category.canDisable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    variant: \"secondary\",\n                                                                    className: \"text-xs\",\n                                                                    children: \"Required\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n                                                                    lineNumber: 150,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n                                                            lineNumber: 146,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-muted-foreground mb-2\",\n                                                            children: category.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n                                                            lineNumber: 155,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-muted-foreground\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"Examples: \"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n                                                                    lineNumber: 159,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                category.examples.join(', ')\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n                                                            lineNumber: 158,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"relative inline-flex items-center cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: preferences[key],\n                                                                onChange: (e)=>handlePreferenceChange(key, e.target.checked),\n                                                                disabled: !category.canDisable,\n                                                                className: \"sr-only peer\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n                                                                lineNumber: 166,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n                                                                lineNumber: 176,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, key, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row gap-3 pt-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            onClick: handleAcceptAll,\n                                            className: \"flex-1 sm:flex-none\",\n                                            children: \"Accept All\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            onClick: handleAcceptNecessary,\n                                            className: \"flex-1 sm:flex-none\",\n                                            children: \"Necessary Only\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"ghost\",\n                                            onClick: ()=>setShowDetails(!showDetails),\n                                            className: \"flex-1 sm:flex-none\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_Cookie_Settings_Shield_Target_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Customize\",\n                                                showDetails ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_Cookie_Settings_Shield_Target_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-4 w-4 ml-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_Cookie_Settings_Shield_Target_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-4 w-4 ml-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 17\n                                        }, this),\n                                        showDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            onClick: handleSavePreferences,\n                                            className: \"flex-1 sm:flex-none\",\n                                            children: \"Save Preferences\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n                lineNumber: 120,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n            lineNumber: 119,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n        lineNumber: 118,\n        columnNumber: 5\n    }, this);\n}\n// Cookie settings component for the settings page\nfunction CookieSettings() {\n    const [preferences, setPreferences] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        necessary: true,\n        analytics: false,\n        marketing: false\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CookieSettings.useEffect\": ()=>{\n            const existingConsent = (0,_lib_cookies__WEBPACK_IMPORTED_MODULE_6__.getConsentFromStorage)();\n            if (existingConsent) {\n                setPreferences({\n                    necessary: existingConsent.necessary,\n                    analytics: existingConsent.analytics,\n                    marketing: existingConsent.marketing\n                });\n            }\n        }\n    }[\"CookieSettings.useEffect\"], []);\n    const handleSave = ()=>{\n        (0,_lib_cookies__WEBPACK_IMPORTED_MODULE_6__.updateConsent)(preferences);\n        (0,_lib_cookies__WEBPACK_IMPORTED_MODULE_6__.updateConsentMode)({\n            ...preferences,\n            timestamp: Date.now(),\n            version: '1.0'\n        });\n    };\n    const handlePreferenceChange = (category, value)=>{\n        if (category === 'necessary') return;\n        setPreferences((prev)=>({\n                ...prev,\n                [category]: value\n            }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n            className: \"p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-semibold mb-4\",\n                    children: \"Cookie Preferences\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n                    lineNumber: 272,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: Object.entries(_lib_cookies__WEBPACK_IMPORTED_MODULE_6__.cookieCategories).map(([key, category])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                getCategoryIcon(key),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: category.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 19\n                                                }, this),\n                                                !category.canDisable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                    variant: \"secondary\",\n                                                    className: \"text-xs\",\n                                                    children: \"Required\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"relative inline-flex items-center cursor-pointer\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"checkbox\",\n                                                    checked: preferences[key],\n                                                    onChange: (e)=>handlePreferenceChange(key, e.target.checked),\n                                                    disabled: !category.canDisable,\n                                                    className: \"sr-only peer\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: category.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-muted-foreground\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: \"Examples: \"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 17\n                                        }, this),\n                                        category.examples.join(', ')\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, key, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n                    lineNumber: 274,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        onClick: handleSave,\n                        children: \"Save Preferences\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n                        lineNumber: 316,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n                    lineNumber: 315,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n            lineNumber: 271,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n        lineNumber: 270,\n        columnNumber: 5\n    }, this);\n}\nfunction getCategoryIcon(category) {\n    switch(category){\n        case 'necessary':\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_Cookie_Settings_Shield_Target_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n                lineNumber: 328,\n                columnNumber: 14\n            }, this);\n        case 'analytics':\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_Cookie_Settings_Shield_Target_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n                lineNumber: 330,\n                columnNumber: 14\n            }, this);\n        case 'marketing':\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_Cookie_Settings_Shield_Target_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n                lineNumber: 332,\n                columnNumber: 14\n            }, this);\n        default:\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_Cookie_Settings_Shield_Target_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\cookies\\\\cookie-consent-banner.tsx\",\n                lineNumber: 334,\n                columnNumber: 14\n            }, this);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/cookies/cookie-consent-banner.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Menu_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,Search,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,Search,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,Search,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./src/components/ui/input.tsx\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/config */ \"(ssr)/./src/lib/config.ts\");\n/* __next_internal_client_entry_do_not_use__ Header auto */ \n\n\n\n\n\n\nfunction Header({ categories = [] }) {\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isSearchOpen, setIsSearchOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex h-16 items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/\",\n                            className: \"flex items-center space-x-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-2xl font-bold text-primary\",\n                                children: _lib_config__WEBPACK_IMPORTED_MODULE_5__.siteConfig.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                lineNumber: 27,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"hidden md:flex items-center space-x-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/\",\n                                    className: \"text-sm font-medium transition-colors hover:text-primary\",\n                                    children: \"Home\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                    lineNumber: 34,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/blog\",\n                                    className: \"text-sm font-medium transition-colors hover:text-primary\",\n                                    children: \"Blog\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 13\n                                }, this),\n                                categories.slice(0, 3).map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: `/blog/category/${category.slug}`,\n                                        className: \"text-sm font-medium transition-colors hover:text-primary\",\n                                        children: category.name\n                                    }, category.slug, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                        lineNumber: 47,\n                                        columnNumber: 15\n                                    }, this)),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/about\",\n                                    className: \"text-sm font-medium transition-colors hover:text-primary\",\n                                    children: \"About\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"icon\",\n                                    onClick: ()=>setIsSearchOpen(!isSearchOpen),\n                                    className: \"md:hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden md:block\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        type: \"search\",\n                                        placeholder: \"Search posts...\",\n                                        className: \"w-64\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"icon\",\n                                    onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                                    className: \"md:hidden\",\n                                    children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, this),\n                isSearchOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"py-4 md:hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                        type: \"search\",\n                        placeholder: \"Search posts...\",\n                        className: \"w-full\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 11\n                }, this),\n                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"py-4 md:hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex flex-col space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/\",\n                                className: \"text-sm font-medium transition-colors hover:text-primary\",\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: \"Home\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/blog\",\n                                className: \"text-sm font-medium transition-colors hover:text-primary\",\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: \"Blog\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 15\n                            }, this),\n                            categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: `/blog/category/${category.slug}`,\n                                    className: \"text-sm font-medium transition-colors hover:text-primary\",\n                                    onClick: ()=>setIsMenuOpen(false),\n                                    children: category.name\n                                }, category.slug, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 17\n                                }, this)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/about\",\n                                className: \"text-sm font-medium transition-colors hover:text-primary\",\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: \"About\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\header.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\layout\\\\header.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/session-provider.tsx":
/*!*******************************************************!*\
  !*** ./src/components/providers/session-provider.tsx ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\providers\\\\session-provider.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMvc2Vzc2lvbi1wcm92aWRlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRWlEO0FBTzFDLFNBQVNDLFVBQVUsRUFBRUMsUUFBUSxFQUFrQjtJQUNwRCxxQkFDRSw4REFBQ0YsNERBQWVBO2tCQUNiRTs7Ozs7O0FBR1AiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVXRlbnRlXFxEZXNrdG9wXFxCb2lsZXJwbGF0ZVxcc3JjXFxjb21wb25lbnRzXFxwcm92aWRlcnNcXHNlc3Npb24tcHJvdmlkZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyBTZXNzaW9uUHJvdmlkZXIgfSBmcm9tICduZXh0LWF1dGgvcmVhY3QnXG5pbXBvcnQgeyBSZWFjdE5vZGUgfSBmcm9tICdyZWFjdCdcblxuaW50ZXJmYWNlIFByb3ZpZGVyc1Byb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0Tm9kZVxufVxuXG5leHBvcnQgZnVuY3Rpb24gUHJvdmlkZXJzKHsgY2hpbGRyZW4gfTogUHJvdmlkZXJzUHJvcHMpIHtcbiAgcmV0dXJuIChcbiAgICA8U2Vzc2lvblByb3ZpZGVyPlxuICAgICAge2NoaWxkcmVufVxuICAgIDwvU2Vzc2lvblByb3ZpZGVyPlxuICApXG59XG4iXSwibmFtZXMiOlsiU2Vzc2lvblByb3ZpZGVyIiwiUHJvdmlkZXJzIiwiY2hpbGRyZW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/session-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/badge.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/badge.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            destructive: \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n            outline: \"text-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\ui\\\\badge.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/badge.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 43,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 8,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 23,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 31,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 46,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 58,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 66,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/input.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/input.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Boilerplate\\\\src\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 10,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9pbnB1dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUE4QjtBQUNFO0FBS2hDLE1BQU1FLHNCQUFRRiw2Q0FBZ0IsQ0FDNUIsQ0FBQyxFQUFFSSxTQUFTLEVBQUVDLElBQUksRUFBRSxHQUFHQyxPQUFPLEVBQUVDO0lBQzlCLHFCQUNFLDhEQUFDQztRQUNDSCxNQUFNQTtRQUNORCxXQUFXSCw4Q0FBRUEsQ0FDWCxnV0FDQUc7UUFFRkcsS0FBS0E7UUFDSixHQUFHRCxLQUFLOzs7Ozs7QUFHZjtBQUVGSixNQUFNTyxXQUFXLEdBQUc7QUFFSiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxVdGVudGVcXERlc2t0b3BcXEJvaWxlcnBsYXRlXFxzcmNcXGNvbXBvbmVudHNcXHVpXFxpbnB1dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCJcblxuZXhwb3J0IGludGVyZmFjZSBJbnB1dFByb3BzXG4gIGV4dGVuZHMgUmVhY3QuSW5wdXRIVE1MQXR0cmlidXRlczxIVE1MSW5wdXRFbGVtZW50PiB7fVxuXG5jb25zdCBJbnB1dCA9IFJlYWN0LmZvcndhcmRSZWY8SFRNTElucHV0RWxlbWVudCwgSW5wdXRQcm9wcz4oXG4gICh7IGNsYXNzTmFtZSwgdHlwZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxpbnB1dFxuICAgICAgICB0eXBlPXt0eXBlfVxuICAgICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgIFwiZmxleCBoLTEwIHctZnVsbCByb3VuZGVkLW1kIGJvcmRlciBib3JkZXItaW5wdXQgYmctYmFja2dyb3VuZCBweC0zIHB5LTIgdGV4dC1zbSByaW5nLW9mZnNldC1iYWNrZ3JvdW5kIGZpbGU6Ym9yZGVyLTAgZmlsZTpiZy10cmFuc3BhcmVudCBmaWxlOnRleHQtc20gZmlsZTpmb250LW1lZGl1bSBwbGFjZWhvbGRlcjp0ZXh0LW11dGVkLWZvcmVncm91bmQgZm9jdXMtdmlzaWJsZTpvdXRsaW5lLW5vbmUgZm9jdXMtdmlzaWJsZTpyaW5nLTIgZm9jdXMtdmlzaWJsZTpyaW5nLXJpbmcgZm9jdXMtdmlzaWJsZTpyaW5nLW9mZnNldC0yIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCBkaXNhYmxlZDpvcGFjaXR5LTUwXCIsXG4gICAgICAgICAgY2xhc3NOYW1lXG4gICAgICAgICl9XG4gICAgICAgIHJlZj17cmVmfVxuICAgICAgICB7Li4ucHJvcHN9XG4gICAgICAvPlxuICAgIClcbiAgfVxuKVxuSW5wdXQuZGlzcGxheU5hbWUgPSBcIklucHV0XCJcblxuZXhwb3J0IHsgSW5wdXQgfVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiY24iLCJJbnB1dCIsImZvcndhcmRSZWYiLCJjbGFzc05hbWUiLCJ0eXBlIiwicHJvcHMiLCJyZWYiLCJpbnB1dCIsImRpc3BsYXlOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/analytics.ts":
/*!******************************!*\
  !*** ./src/lib/analytics.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GA_MEASUREMENT_ID: () => (/* binding */ GA_MEASUREMENT_ID),\n/* harmony export */   initConsentMode: () => (/* binding */ initConsentMode),\n/* harmony export */   initGA: () => (/* binding */ initGA),\n/* harmony export */   isAnalyticsEnabled: () => (/* binding */ isAnalyticsEnabled),\n/* harmony export */   trackCategoryClick: () => (/* binding */ trackCategoryClick),\n/* harmony export */   trackDownload: () => (/* binding */ trackDownload),\n/* harmony export */   trackEngagement: () => (/* binding */ trackEngagement),\n/* harmony export */   trackError: () => (/* binding */ trackError),\n/* harmony export */   trackEvent: () => (/* binding */ trackEvent),\n/* harmony export */   trackFormSubmission: () => (/* binding */ trackFormSubmission),\n/* harmony export */   trackNewsletterSignup: () => (/* binding */ trackNewsletterSignup),\n/* harmony export */   trackOutboundLink: () => (/* binding */ trackOutboundLink),\n/* harmony export */   trackPageView: () => (/* binding */ trackPageView),\n/* harmony export */   trackPerformance: () => (/* binding */ trackPerformance),\n/* harmony export */   trackPostView: () => (/* binding */ trackPostView),\n/* harmony export */   trackPurchase: () => (/* binding */ trackPurchase),\n/* harmony export */   trackScroll: () => (/* binding */ trackScroll),\n/* harmony export */   trackSearch: () => (/* binding */ trackSearch),\n/* harmony export */   trackSocialShare: () => (/* binding */ trackSocialShare),\n/* harmony export */   trackTagClick: () => (/* binding */ trackTagClick),\n/* harmony export */   trackVideoComplete: () => (/* binding */ trackVideoComplete),\n/* harmony export */   trackVideoPlay: () => (/* binding */ trackVideoPlay),\n/* harmony export */   updateConsentMode: () => (/* binding */ updateConsentMode)\n/* harmony export */ });\n/* __next_internal_client_entry_do_not_use__ GA_MEASUREMENT_ID,initGA,trackPageView,trackEvent,trackPostView,trackCategoryClick,trackTagClick,trackSearch,trackNewsletterSignup,trackSocialShare,trackDownload,trackOutboundLink,trackPurchase,trackEngagement,trackScroll,trackFormSubmission,trackVideoPlay,trackVideoComplete,trackError,trackPerformance,updateConsentMode,initConsentMode,isAnalyticsEnabled auto */ const GA_MEASUREMENT_ID = \"G-XXXXXXXXXX\";\n// Initialize Google Analytics\nconst initGA = (measurementId)=>{\n    if (true) return;\n    // Create script tag for gtag\n    const script = document.createElement('script');\n    script.src = `https://www.googletagmanager.com/gtag/js?id=${measurementId}`;\n    script.async = true;\n    document.head.appendChild(script);\n    // Initialize dataLayer and gtag\n    window.dataLayer = window.dataLayer || [];\n    window.gtag = function gtag() {\n        window.dataLayer.push(arguments);\n    };\n    window.gtag('js', new Date());\n    window.gtag('config', measurementId, {\n        page_title: document.title,\n        page_location: window.location.href\n    });\n};\n// Track page views\nconst trackPageView = (url, title)=>{\n    if (true) return;\n    window.gtag('config', GA_MEASUREMENT_ID, {\n        page_title: title || document.title,\n        page_location: url\n    });\n};\n// Track custom events\nconst trackEvent = (action, category, label, value)=>{\n    if (true) return;\n    window.gtag('event', action, {\n        event_category: category,\n        event_label: label,\n        value: value\n    });\n};\n// Specific event tracking functions\nconst trackPostView = (postId, postTitle, category)=>{\n    trackEvent('view_item', 'engagement', `post_${postId}`);\n    trackEvent('page_view', 'blog_post', postTitle);\n    if (category) {\n        trackEvent('view_category', 'navigation', category);\n    }\n};\nconst trackCategoryClick = (categoryName, categorySlug)=>{\n    trackEvent('click', 'navigation', `category_${categorySlug}`);\n    trackEvent('view_category', 'engagement', categoryName);\n};\nconst trackTagClick = (tagName, tagSlug)=>{\n    trackEvent('click', 'navigation', `tag_${tagSlug}`);\n    trackEvent('view_tag', 'engagement', tagName);\n};\nconst trackSearch = (searchTerm, resultsCount)=>{\n    trackEvent('search', 'engagement', searchTerm, resultsCount);\n};\nconst trackNewsletterSignup = (location)=>{\n    trackEvent('sign_up', 'engagement', `newsletter_${location}`);\n};\nconst trackSocialShare = (platform, postTitle)=>{\n    trackEvent('share', 'engagement', `${platform}_${postTitle}`);\n};\nconst trackDownload = (fileName, fileType)=>{\n    trackEvent('file_download', 'engagement', `${fileType}_${fileName}`);\n};\nconst trackOutboundLink = (url, linkText)=>{\n    trackEvent('click', 'outbound_link', linkText || url);\n};\n// Enhanced ecommerce events (for future use)\nconst trackPurchase = (transactionId, value, currency = 'USD')=>{\n    if (true) return;\n    window.gtag('event', 'purchase', {\n        transaction_id: transactionId,\n        value: value,\n        currency: currency\n    });\n};\n// User engagement tracking\nconst trackEngagement = (engagementTime)=>{\n    if (true) return;\n    window.gtag('event', 'user_engagement', {\n        engagement_time_msec: engagementTime\n    });\n};\n// Scroll tracking\nconst trackScroll = (scrollPercentage)=>{\n    trackEvent('scroll', 'engagement', `${scrollPercentage}%`, scrollPercentage);\n};\n// Form tracking\nconst trackFormSubmission = (formName, success)=>{\n    trackEvent(success ? 'form_submit' : 'form_error', 'engagement', formName);\n};\n// Video tracking\nconst trackVideoPlay = (videoTitle, videoUrl)=>{\n    trackEvent('video_play', 'engagement', videoTitle);\n};\nconst trackVideoComplete = (videoTitle, duration)=>{\n    trackEvent('video_complete', 'engagement', videoTitle, duration);\n};\n// Error tracking\nconst trackError = (errorMessage, errorLocation)=>{\n    trackEvent('exception', 'error', `${errorLocation}: ${errorMessage}`);\n};\n// Performance tracking\nconst trackPerformance = (metricName, value)=>{\n    if (true) return;\n    window.gtag('event', 'timing_complete', {\n        name: metricName,\n        value: Math.round(value)\n    });\n};\n// Consent management for GDPR compliance\nconst updateConsentMode = (analyticsConsent, adConsent = 'denied')=>{\n    if (true) return;\n    window.gtag('consent', 'update', {\n        analytics_storage: analyticsConsent,\n        ad_storage: adConsent,\n        ad_user_data: adConsent,\n        ad_personalization: adConsent\n    });\n};\n// Initialize consent mode (should be called before GA initialization)\nconst initConsentMode = ()=>{\n    if (true) return;\n    window.dataLayer = window.dataLayer || [];\n    window.gtag = window.gtag || function() {\n        window.dataLayer.push(arguments);\n    };\n    // Set default consent mode\n    window.gtag('consent', 'default', {\n        analytics_storage: 'denied',\n        ad_storage: 'denied',\n        ad_user_data: 'denied',\n        ad_personalization: 'denied',\n        wait_for_update: 500\n    });\n};\n// Check if analytics is enabled\nconst isAnalyticsEnabled = ()=>{\n    if (true) return false;\n    const consent = localStorage.getItem('cookie-consent');\n    if (!consent) return false;\n    try {\n        const consentData = JSON.parse(consent);\n        return consentData.analytics === true;\n    } catch  {\n        return false;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/analytics.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/config.ts":
/*!***************************!*\
  !*** ./src/lib/config.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ALLOWED_IMAGE_TYPES: () => (/* binding */ ALLOWED_IMAGE_TYPES),\n/* harmony export */   ALLOWED_VIDEO_TYPES: () => (/* binding */ ALLOWED_VIDEO_TYPES),\n/* harmony export */   MAX_FILE_SIZE: () => (/* binding */ MAX_FILE_SIZE),\n/* harmony export */   POSTS_PER_PAGE: () => (/* binding */ POSTS_PER_PAGE),\n/* harmony export */   defaultTheme: () => (/* binding */ defaultTheme),\n/* harmony export */   siteConfig: () => (/* binding */ siteConfig)\n/* harmony export */ });\nconst siteConfig = {\n    name: \"My Blog\" || 0,\n    description: \"A modern blog built with Next.js\" || 0,\n    url: \"http://localhost:3000\" || 0,\n    ogImage: '/og-image.jpg',\n    links: {\n        twitter: 'https://twitter.com/yourusername',\n        github: 'https://github.com/yourusername',\n        instagram: 'https://instagram.com/yourusername'\n    }\n};\nconst defaultTheme = {\n    colors: {\n        primary: '#3B82F6',\n        secondary: '#64748B',\n        accent: '#F59E0B',\n        background: '#FFFFFF',\n        foreground: '#0F172A'\n    },\n    fonts: {\n        heading: 'Inter, sans-serif',\n        body: 'Inter, sans-serif'\n    },\n    spacing: {\n        container: '1200px'\n    }\n};\nconst POSTS_PER_PAGE = 10;\nconst MAX_FILE_SIZE = parseInt(\"5242880\" || 0) // 5MB\n;\nconst ALLOWED_IMAGE_TYPES = [\n    'image/jpeg',\n    'image/png',\n    'image/webp',\n    'image/gif'\n];\nconst ALLOWED_VIDEO_TYPES = [\n    'video/mp4',\n    'video/webm',\n    'video/ogg'\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL2NvbmZpZy50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFFTyxNQUFNQSxhQUF5QjtJQUNwQ0MsTUFBTUMsU0FBaUMsSUFBSSxDQUFrQjtJQUM3REcsYUFBYUgsa0NBQXdDLElBQUksQ0FBa0M7SUFDM0ZLLEtBQUtMLHVCQUFnQyxJQUFJLENBQXVCO0lBQ2hFTyxTQUFTO0lBQ1RDLE9BQU87UUFDTEMsU0FBUztRQUNUQyxRQUFRO1FBQ1JDLFdBQVc7SUFDYjtBQUNGLEVBQUM7QUFFTSxNQUFNQyxlQUE0QjtJQUN2Q0MsUUFBUTtRQUNOQyxTQUFTO1FBQ1RDLFdBQVc7UUFDWEMsUUFBUTtRQUNSQyxZQUFZO1FBQ1pDLFlBQVk7SUFDZDtJQUNBQyxPQUFPO1FBQ0xDLFNBQVM7UUFDVEMsTUFBTTtJQUNSO0lBQ0FDLFNBQVM7UUFDUEMsV0FBVztJQUNiO0FBQ0YsRUFBQztBQUVNLE1BQU1DLGlCQUFpQixHQUFFO0FBQ3pCLE1BQU1DLGdCQUFnQkMsU0FBUzFCLFNBQXFDLElBQUksQ0FBUyxFQUFFLE1BQU07Q0FBUDtBQUNsRixNQUFNNEIsc0JBQXNCO0lBQUM7SUFBYztJQUFhO0lBQWM7Q0FBWTtBQUNsRixNQUFNQyxzQkFBc0I7SUFBQztJQUFhO0lBQWM7Q0FBWSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxVdGVudGVcXERlc2t0b3BcXEJvaWxlcnBsYXRlXFxzcmNcXGxpYlxcY29uZmlnLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFNpdGVDb25maWcsIFRoZW1lQ29uZmlnIH0gZnJvbSAnQC90eXBlcydcblxuZXhwb3J0IGNvbnN0IHNpdGVDb25maWc6IFNpdGVDb25maWcgPSB7XG4gIG5hbWU6IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NJVEVfTkFNRSB8fCAnQmxvZyBCb2lsZXJwbGF0ZScsXG4gIGRlc2NyaXB0aW9uOiBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TSVRFX0RFU0NSSVBUSU9OIHx8ICdBIG1vZGVybiBibG9nIGJ1aWx0IHdpdGggTmV4dC5qcycsXG4gIHVybDogcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU0lURV9VUkwgfHwgJ2h0dHA6Ly9sb2NhbGhvc3Q6MzAwMCcsXG4gIG9nSW1hZ2U6ICcvb2ctaW1hZ2UuanBnJyxcbiAgbGlua3M6IHtcbiAgICB0d2l0dGVyOiAnaHR0cHM6Ly90d2l0dGVyLmNvbS95b3VydXNlcm5hbWUnLFxuICAgIGdpdGh1YjogJ2h0dHBzOi8vZ2l0aHViLmNvbS95b3VydXNlcm5hbWUnLFxuICAgIGluc3RhZ3JhbTogJ2h0dHBzOi8vaW5zdGFncmFtLmNvbS95b3VydXNlcm5hbWUnLFxuICB9LFxufVxuXG5leHBvcnQgY29uc3QgZGVmYXVsdFRoZW1lOiBUaGVtZUNvbmZpZyA9IHtcbiAgY29sb3JzOiB7XG4gICAgcHJpbWFyeTogJyMzQjgyRjYnLFxuICAgIHNlY29uZGFyeTogJyM2NDc0OEInLFxuICAgIGFjY2VudDogJyNGNTlFMEInLFxuICAgIGJhY2tncm91bmQ6ICcjRkZGRkZGJyxcbiAgICBmb3JlZ3JvdW5kOiAnIzBGMTcyQScsXG4gIH0sXG4gIGZvbnRzOiB7XG4gICAgaGVhZGluZzogJ0ludGVyLCBzYW5zLXNlcmlmJyxcbiAgICBib2R5OiAnSW50ZXIsIHNhbnMtc2VyaWYnLFxuICB9LFxuICBzcGFjaW5nOiB7XG4gICAgY29udGFpbmVyOiAnMTIwMHB4JyxcbiAgfSxcbn1cblxuZXhwb3J0IGNvbnN0IFBPU1RTX1BFUl9QQUdFID0gMTBcbmV4cG9ydCBjb25zdCBNQVhfRklMRV9TSVpFID0gcGFyc2VJbnQocHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfTUFYX0ZJTEVfU0laRSB8fCAnNTI0Mjg4MCcpIC8vIDVNQlxuZXhwb3J0IGNvbnN0IEFMTE9XRURfSU1BR0VfVFlQRVMgPSBbJ2ltYWdlL2pwZWcnLCAnaW1hZ2UvcG5nJywgJ2ltYWdlL3dlYnAnLCAnaW1hZ2UvZ2lmJ11cbmV4cG9ydCBjb25zdCBBTExPV0VEX1ZJREVPX1RZUEVTID0gWyd2aWRlby9tcDQnLCAndmlkZW8vd2VibScsICd2aWRlby9vZ2cnXVxuIl0sIm5hbWVzIjpbInNpdGVDb25maWciLCJuYW1lIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX1NJVEVfTkFNRSIsImRlc2NyaXB0aW9uIiwiTkVYVF9QVUJMSUNfU0lURV9ERVNDUklQVElPTiIsInVybCIsIk5FWFRfUFVCTElDX1NJVEVfVVJMIiwib2dJbWFnZSIsImxpbmtzIiwidHdpdHRlciIsImdpdGh1YiIsImluc3RhZ3JhbSIsImRlZmF1bHRUaGVtZSIsImNvbG9ycyIsInByaW1hcnkiLCJzZWNvbmRhcnkiLCJhY2NlbnQiLCJiYWNrZ3JvdW5kIiwiZm9yZWdyb3VuZCIsImZvbnRzIiwiaGVhZGluZyIsImJvZHkiLCJzcGFjaW5nIiwiY29udGFpbmVyIiwiUE9TVFNfUEVSX1BBR0UiLCJNQVhfRklMRV9TSVpFIiwicGFyc2VJbnQiLCJORVhUX1BVQkxJQ19NQVhfRklMRV9TSVpFIiwiQUxMT1dFRF9JTUFHRV9UWVBFUyIsIkFMTE9XRURfVklERU9fVFlQRVMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/config.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/cookies.ts":
/*!****************************!*\
  !*** ./src/lib/cookies.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   COOKIE_CONSENT_KEY: () => (/* binding */ COOKIE_CONSENT_KEY),\n/* harmony export */   COOKIE_CONSENT_VERSION: () => (/* binding */ COOKIE_CONSENT_VERSION),\n/* harmony export */   acceptAllCookies: () => (/* binding */ acceptAllCookies),\n/* harmony export */   acceptNecessaryCookies: () => (/* binding */ acceptNecessaryCookies),\n/* harmony export */   clearConsent: () => (/* binding */ clearConsent),\n/* harmony export */   cookieCategories: () => (/* binding */ cookieCategories),\n/* harmony export */   defaultConsent: () => (/* binding */ defaultConsent),\n/* harmony export */   getConsentExpiryDate: () => (/* binding */ getConsentExpiryDate),\n/* harmony export */   getConsentFromStorage: () => (/* binding */ getConsentFromStorage),\n/* harmony export */   hasConsent: () => (/* binding */ hasConsent),\n/* harmony export */   initializeConsentMode: () => (/* binding */ initializeConsentMode),\n/* harmony export */   isConsentExpired: () => (/* binding */ isConsentExpired),\n/* harmony export */   saveConsentToStorage: () => (/* binding */ saveConsentToStorage),\n/* harmony export */   shouldShowConsentBanner: () => (/* binding */ shouldShowConsentBanner),\n/* harmony export */   updateConsent: () => (/* binding */ updateConsent),\n/* harmony export */   updateConsentMode: () => (/* binding */ updateConsentMode)\n/* harmony export */ });\n/* __next_internal_client_entry_do_not_use__ COOKIE_CONSENT_VERSION,COOKIE_CONSENT_KEY,defaultConsent,getConsentFromStorage,saveConsentToStorage,hasConsent,shouldShowConsentBanner,acceptAllCookies,acceptNecessaryCookies,updateConsent,clearConsent,getConsentExpiryDate,isConsentExpired,cookieCategories,initializeConsentMode,updateConsentMode auto */ const COOKIE_CONSENT_VERSION = '1.0';\nconst COOKIE_CONSENT_KEY = 'cookie-consent';\n// Default consent (only necessary cookies)\nconst defaultConsent = {\n    necessary: true,\n    analytics: false,\n    marketing: false,\n    timestamp: Date.now(),\n    version: COOKIE_CONSENT_VERSION\n};\n// Get current consent from localStorage\nconst getConsentFromStorage = ()=>{\n    if (true) return null;\n    try {\n        const stored = localStorage.getItem(COOKIE_CONSENT_KEY);\n        if (!stored) return null;\n        const consent = JSON.parse(stored);\n        // Check if consent is outdated\n        if (consent.version !== COOKIE_CONSENT_VERSION) {\n            return null;\n        }\n        return consent;\n    } catch (error) {\n        console.error('Error parsing cookie consent:', error);\n        return null;\n    }\n};\n// Save consent to localStorage\nconst saveConsentToStorage = (consent)=>{\n    if (true) return;\n    try {\n        const consentWithTimestamp = {\n            ...consent,\n            timestamp: Date.now(),\n            version: COOKIE_CONSENT_VERSION\n        };\n        localStorage.setItem(COOKIE_CONSENT_KEY, JSON.stringify(consentWithTimestamp));\n        // Dispatch custom event for other components to listen\n        window.dispatchEvent(new CustomEvent('cookieConsentChanged', {\n            detail: consentWithTimestamp\n        }));\n    } catch (error) {\n        console.error('Error saving cookie consent:', error);\n    }\n};\n// Check if user has given consent for a specific category\nconst hasConsent = (category)=>{\n    const consent = getConsentFromStorage();\n    if (!consent) return category === 'necessary' // Only necessary cookies by default\n    ;\n    return consent[category];\n};\n// Check if consent banner should be shown\nconst shouldShowConsentBanner = ()=>{\n    return getConsentFromStorage() === null;\n};\n// Accept all cookies\nconst acceptAllCookies = ()=>{\n    const consent = {\n        necessary: true,\n        analytics: true,\n        marketing: true,\n        timestamp: Date.now(),\n        version: COOKIE_CONSENT_VERSION\n    };\n    saveConsentToStorage(consent);\n};\n// Accept only necessary cookies\nconst acceptNecessaryCookies = ()=>{\n    saveConsentToStorage(defaultConsent);\n};\n// Update specific consent preferences\nconst updateConsent = (updates)=>{\n    const currentConsent = getConsentFromStorage() || defaultConsent;\n    const newConsent = {\n        ...currentConsent,\n        ...updates,\n        necessary: true,\n        timestamp: Date.now(),\n        version: COOKIE_CONSENT_VERSION\n    };\n    saveConsentToStorage(newConsent);\n};\n// Clear all consent (for testing or reset)\nconst clearConsent = ()=>{\n    if (true) return;\n    localStorage.removeItem(COOKIE_CONSENT_KEY);\n    window.dispatchEvent(new CustomEvent('cookieConsentChanged', {\n        detail: null\n    }));\n};\n// Get consent expiry date (1 year from consent date)\nconst getConsentExpiryDate = ()=>{\n    const consent = getConsentFromStorage();\n    if (!consent) return null;\n    const expiryDate = new Date(consent.timestamp);\n    expiryDate.setFullYear(expiryDate.getFullYear() + 1);\n    return expiryDate;\n};\n// Check if consent has expired\nconst isConsentExpired = ()=>{\n    const expiryDate = getConsentExpiryDate();\n    if (!expiryDate) return true;\n    return new Date() > expiryDate;\n};\n// Cookie categories information\nconst cookieCategories = {\n    necessary: {\n        name: 'Necessary',\n        description: 'These cookies are essential for the website to function properly. They enable basic features like page navigation and access to secure areas.',\n        examples: [\n            'Session cookies',\n            'Authentication tokens',\n            'Security cookies'\n        ],\n        canDisable: false\n    },\n    analytics: {\n        name: 'Analytics',\n        description: 'These cookies help us understand how visitors interact with our website by collecting and reporting information anonymously.',\n        examples: [\n            'Google Analytics',\n            'Page view tracking',\n            'User behavior analysis'\n        ],\n        canDisable: true\n    },\n    marketing: {\n        name: 'Marketing',\n        description: 'These cookies are used to track visitors across websites to display relevant advertisements and marketing content.',\n        examples: [\n            'Social media pixels',\n            'Advertising cookies',\n            'Retargeting cookies'\n        ],\n        canDisable: true\n    }\n};\n// Initialize consent mode for Google Analytics\nconst initializeConsentMode = ()=>{\n    if (true) return;\n    const consent = getConsentFromStorage();\n    // Set up Google Consent Mode\n    if (window.gtag) {\n        window.gtag('consent', 'default', {\n            analytics_storage: consent?.analytics ? 'granted' : 'denied',\n            ad_storage: consent?.marketing ? 'granted' : 'denied',\n            ad_user_data: consent?.marketing ? 'granted' : 'denied',\n            ad_personalization: consent?.marketing ? 'granted' : 'denied'\n        });\n    }\n};\n// Update Google Consent Mode when consent changes\nconst updateConsentMode = (consent)=>{\n    if (true) return;\n    window.gtag('consent', 'update', {\n        analytics_storage: consent.analytics ? 'granted' : 'denied',\n        ad_storage: consent.marketing ? 'granted' : 'denied',\n        ad_user_data: consent.marketing ? 'granted' : 'denied',\n        ad_personalization: consent.marketing ? 'granted' : 'denied'\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL2Nvb2tpZXMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4VkFVTyxNQUFNQSx5QkFBeUIsTUFBSztBQUNwQyxNQUFNQyxxQkFBcUIsaUJBQWdCO0FBRWxELDJDQUEyQztBQUNwQyxNQUFNQyxpQkFBZ0M7SUFDM0NDLFdBQVc7SUFDWEMsV0FBVztJQUNYQyxXQUFXO0lBQ1hDLFdBQVdDLEtBQUtDLEdBQUc7SUFDbkJDLFNBQVNUO0FBQ1gsRUFBQztBQUVELHdDQUF3QztBQUNqQyxNQUFNVSx3QkFBd0I7SUFDbkMsSUFBSSxJQUE2QixFQUFFLE9BQU87SUFFMUMsSUFBSTtRQUNGLE1BQU1DLFNBQVNDLGFBQWFDLE9BQU8sQ0FBQ1o7UUFDcEMsSUFBSSxDQUFDVSxRQUFRLE9BQU87UUFFcEIsTUFBTUcsVUFBVUMsS0FBS0MsS0FBSyxDQUFDTDtRQUUzQiwrQkFBK0I7UUFDL0IsSUFBSUcsUUFBUUwsT0FBTyxLQUFLVCx3QkFBd0I7WUFDOUMsT0FBTztRQUNUO1FBRUEsT0FBT2M7SUFDVCxFQUFFLE9BQU9HLE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLGlDQUFpQ0E7UUFDL0MsT0FBTztJQUNUO0FBQ0YsRUFBQztBQUVELCtCQUErQjtBQUN4QixNQUFNRSx1QkFBdUIsQ0FBQ0w7SUFDbkMsSUFBSSxJQUE2QixFQUFFO0lBRW5DLElBQUk7UUFDRixNQUFNTSx1QkFBdUI7WUFDM0IsR0FBR04sT0FBTztZQUNWUixXQUFXQyxLQUFLQyxHQUFHO1lBQ25CQyxTQUFTVDtRQUNYO1FBRUFZLGFBQWFTLE9BQU8sQ0FBQ3BCLG9CQUFvQmMsS0FBS08sU0FBUyxDQUFDRjtRQUV4RCx1REFBdUQ7UUFDdkRHLE9BQU9DLGFBQWEsQ0FBQyxJQUFJQyxZQUFZLHdCQUF3QjtZQUMzREMsUUFBUU47UUFDVjtJQUNGLEVBQUUsT0FBT0gsT0FBTztRQUNkQyxRQUFRRCxLQUFLLENBQUMsZ0NBQWdDQTtJQUNoRDtBQUNGLEVBQUM7QUFFRCwwREFBMEQ7QUFDbkQsTUFBTVUsYUFBYSxDQUFDQztJQUN6QixNQUFNZCxVQUFVSjtJQUNoQixJQUFJLENBQUNJLFNBQVMsT0FBT2MsYUFBYSxZQUFZLG9DQUFvQzs7SUFFbEYsT0FBT2QsT0FBTyxDQUFDYyxTQUFTO0FBQzFCLEVBQUM7QUFFRCwwQ0FBMEM7QUFDbkMsTUFBTUMsMEJBQTBCO0lBQ3JDLE9BQU9uQiw0QkFBNEI7QUFDckMsRUFBQztBQUVELHFCQUFxQjtBQUNkLE1BQU1vQixtQkFBbUI7SUFDOUIsTUFBTWhCLFVBQXlCO1FBQzdCWCxXQUFXO1FBQ1hDLFdBQVc7UUFDWEMsV0FBVztRQUNYQyxXQUFXQyxLQUFLQyxHQUFHO1FBQ25CQyxTQUFTVDtJQUNYO0lBRUFtQixxQkFBcUJMO0FBQ3ZCLEVBQUM7QUFFRCxnQ0FBZ0M7QUFDekIsTUFBTWlCLHlCQUF5QjtJQUNwQ1oscUJBQXFCakI7QUFDdkIsRUFBQztBQUVELHNDQUFzQztBQUMvQixNQUFNOEIsZ0JBQWdCLENBQUNDO0lBQzVCLE1BQU1DLGlCQUFpQnhCLDJCQUEyQlI7SUFFbEQsTUFBTWlDLGFBQTRCO1FBQ2hDLEdBQUdELGNBQWM7UUFDakIsR0FBR0QsT0FBTztRQUNWOUIsV0FBVztRQUNYRyxXQUFXQyxLQUFLQyxHQUFHO1FBQ25CQyxTQUFTVDtJQUNYO0lBRUFtQixxQkFBcUJnQjtBQUN2QixFQUFDO0FBRUQsMkNBQTJDO0FBQ3BDLE1BQU1DLGVBQWU7SUFDMUIsSUFBSSxJQUE2QixFQUFFO0lBRW5DeEIsYUFBYXlCLFVBQVUsQ0FBQ3BDO0lBQ3hCc0IsT0FBT0MsYUFBYSxDQUFDLElBQUlDLFlBQVksd0JBQXdCO1FBQzNEQyxRQUFRO0lBQ1Y7QUFDRixFQUFDO0FBRUQscURBQXFEO0FBQzlDLE1BQU1ZLHVCQUF1QjtJQUNsQyxNQUFNeEIsVUFBVUo7SUFDaEIsSUFBSSxDQUFDSSxTQUFTLE9BQU87SUFFckIsTUFBTXlCLGFBQWEsSUFBSWhDLEtBQUtPLFFBQVFSLFNBQVM7SUFDN0NpQyxXQUFXQyxXQUFXLENBQUNELFdBQVdFLFdBQVcsS0FBSztJQUVsRCxPQUFPRjtBQUNULEVBQUM7QUFFRCwrQkFBK0I7QUFDeEIsTUFBTUcsbUJBQW1CO0lBQzlCLE1BQU1ILGFBQWFEO0lBQ25CLElBQUksQ0FBQ0MsWUFBWSxPQUFPO0lBRXhCLE9BQU8sSUFBSWhDLFNBQVNnQztBQUN0QixFQUFDO0FBRUQsZ0NBQWdDO0FBQ3pCLE1BQU1JLG1CQUFtQjtJQUM5QnhDLFdBQVc7UUFDVHlDLE1BQU07UUFDTkMsYUFBYTtRQUNiQyxVQUFVO1lBQUM7WUFBbUI7WUFBeUI7U0FBbUI7UUFDMUVDLFlBQVk7SUFDZDtJQUNBM0MsV0FBVztRQUNUd0MsTUFBTTtRQUNOQyxhQUFhO1FBQ2JDLFVBQVU7WUFBQztZQUFvQjtZQUFzQjtTQUF5QjtRQUM5RUMsWUFBWTtJQUNkO0lBQ0ExQyxXQUFXO1FBQ1R1QyxNQUFNO1FBQ05DLGFBQWE7UUFDYkMsVUFBVTtZQUFDO1lBQXVCO1lBQXVCO1NBQXNCO1FBQy9FQyxZQUFZO0lBQ2Q7QUFDRixFQUFDO0FBRUQsK0NBQStDO0FBQ3hDLE1BQU1DLHdCQUF3QjtJQUNuQyxJQUFJLElBQTZCLEVBQUU7SUFFbkMsTUFBTWxDLFVBQVVKO0lBRWhCLDZCQUE2QjtJQUM3QixJQUFJYSxPQUFPMEIsSUFBSSxFQUFFO1FBQ2YxQixPQUFPMEIsSUFBSSxDQUFDLFdBQVcsV0FBVztZQUNoQ0MsbUJBQW1CcEMsU0FBU1YsWUFBWSxZQUFZO1lBQ3BEK0MsWUFBWXJDLFNBQVNULFlBQVksWUFBWTtZQUM3QytDLGNBQWN0QyxTQUFTVCxZQUFZLFlBQVk7WUFDL0NnRCxvQkFBb0J2QyxTQUFTVCxZQUFZLFlBQVk7UUFDdkQ7SUFDRjtBQUNGLEVBQUM7QUFFRCxrREFBa0Q7QUFDM0MsTUFBTWlELG9CQUFvQixDQUFDeEM7SUFDaEMsSUFBSSxJQUE2QyxFQUFFO0lBRW5EUyxPQUFPMEIsSUFBSSxDQUFDLFdBQVcsVUFBVTtRQUMvQkMsbUJBQW1CcEMsUUFBUVYsU0FBUyxHQUFHLFlBQVk7UUFDbkQrQyxZQUFZckMsUUFBUVQsU0FBUyxHQUFHLFlBQVk7UUFDNUMrQyxjQUFjdEMsUUFBUVQsU0FBUyxHQUFHLFlBQVk7UUFDOUNnRCxvQkFBb0J2QyxRQUFRVCxTQUFTLEdBQUcsWUFBWTtJQUN0RDtBQUNGLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVXRlbnRlXFxEZXNrdG9wXFxCb2lsZXJwbGF0ZVxcc3JjXFxsaWJcXGNvb2tpZXMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmV4cG9ydCBpbnRlcmZhY2UgQ29va2llQ29uc2VudCB7XG4gIG5lY2Vzc2FyeTogYm9vbGVhblxuICBhbmFseXRpY3M6IGJvb2xlYW5cbiAgbWFya2V0aW5nOiBib29sZWFuXG4gIHRpbWVzdGFtcDogbnVtYmVyXG4gIHZlcnNpb246IHN0cmluZ1xufVxuXG5leHBvcnQgY29uc3QgQ09PS0lFX0NPTlNFTlRfVkVSU0lPTiA9ICcxLjAnXG5leHBvcnQgY29uc3QgQ09PS0lFX0NPTlNFTlRfS0VZID0gJ2Nvb2tpZS1jb25zZW50J1xuXG4vLyBEZWZhdWx0IGNvbnNlbnQgKG9ubHkgbmVjZXNzYXJ5IGNvb2tpZXMpXG5leHBvcnQgY29uc3QgZGVmYXVsdENvbnNlbnQ6IENvb2tpZUNvbnNlbnQgPSB7XG4gIG5lY2Vzc2FyeTogdHJ1ZSxcbiAgYW5hbHl0aWNzOiBmYWxzZSxcbiAgbWFya2V0aW5nOiBmYWxzZSxcbiAgdGltZXN0YW1wOiBEYXRlLm5vdygpLFxuICB2ZXJzaW9uOiBDT09LSUVfQ09OU0VOVF9WRVJTSU9OLFxufVxuXG4vLyBHZXQgY3VycmVudCBjb25zZW50IGZyb20gbG9jYWxTdG9yYWdlXG5leHBvcnQgY29uc3QgZ2V0Q29uc2VudEZyb21TdG9yYWdlID0gKCk6IENvb2tpZUNvbnNlbnQgfCBudWxsID0+IHtcbiAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSByZXR1cm4gbnVsbFxuXG4gIHRyeSB7XG4gICAgY29uc3Qgc3RvcmVkID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oQ09PS0lFX0NPTlNFTlRfS0VZKVxuICAgIGlmICghc3RvcmVkKSByZXR1cm4gbnVsbFxuXG4gICAgY29uc3QgY29uc2VudCA9IEpTT04ucGFyc2Uoc3RvcmVkKSBhcyBDb29raWVDb25zZW50XG4gICAgXG4gICAgLy8gQ2hlY2sgaWYgY29uc2VudCBpcyBvdXRkYXRlZFxuICAgIGlmIChjb25zZW50LnZlcnNpb24gIT09IENPT0tJRV9DT05TRU5UX1ZFUlNJT04pIHtcbiAgICAgIHJldHVybiBudWxsXG4gICAgfVxuXG4gICAgcmV0dXJuIGNvbnNlbnRcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBwYXJzaW5nIGNvb2tpZSBjb25zZW50OicsIGVycm9yKVxuICAgIHJldHVybiBudWxsXG4gIH1cbn1cblxuLy8gU2F2ZSBjb25zZW50IHRvIGxvY2FsU3RvcmFnZVxuZXhwb3J0IGNvbnN0IHNhdmVDb25zZW50VG9TdG9yYWdlID0gKGNvbnNlbnQ6IENvb2tpZUNvbnNlbnQpOiB2b2lkID0+IHtcbiAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSByZXR1cm5cblxuICB0cnkge1xuICAgIGNvbnN0IGNvbnNlbnRXaXRoVGltZXN0YW1wID0ge1xuICAgICAgLi4uY29uc2VudCxcbiAgICAgIHRpbWVzdGFtcDogRGF0ZS5ub3coKSxcbiAgICAgIHZlcnNpb246IENPT0tJRV9DT05TRU5UX1ZFUlNJT04sXG4gICAgfVxuICAgIFxuICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKENPT0tJRV9DT05TRU5UX0tFWSwgSlNPTi5zdHJpbmdpZnkoY29uc2VudFdpdGhUaW1lc3RhbXApKVxuICAgIFxuICAgIC8vIERpc3BhdGNoIGN1c3RvbSBldmVudCBmb3Igb3RoZXIgY29tcG9uZW50cyB0byBsaXN0ZW5cbiAgICB3aW5kb3cuZGlzcGF0Y2hFdmVudChuZXcgQ3VzdG9tRXZlbnQoJ2Nvb2tpZUNvbnNlbnRDaGFuZ2VkJywge1xuICAgICAgZGV0YWlsOiBjb25zZW50V2l0aFRpbWVzdGFtcFxuICAgIH0pKVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHNhdmluZyBjb29raWUgY29uc2VudDonLCBlcnJvcilcbiAgfVxufVxuXG4vLyBDaGVjayBpZiB1c2VyIGhhcyBnaXZlbiBjb25zZW50IGZvciBhIHNwZWNpZmljIGNhdGVnb3J5XG5leHBvcnQgY29uc3QgaGFzQ29uc2VudCA9IChjYXRlZ29yeToga2V5b2YgT21pdDxDb29raWVDb25zZW50LCAndGltZXN0YW1wJyB8ICd2ZXJzaW9uJz4pOiBib29sZWFuID0+IHtcbiAgY29uc3QgY29uc2VudCA9IGdldENvbnNlbnRGcm9tU3RvcmFnZSgpXG4gIGlmICghY29uc2VudCkgcmV0dXJuIGNhdGVnb3J5ID09PSAnbmVjZXNzYXJ5JyAvLyBPbmx5IG5lY2Vzc2FyeSBjb29raWVzIGJ5IGRlZmF1bHRcbiAgXG4gIHJldHVybiBjb25zZW50W2NhdGVnb3J5XVxufVxuXG4vLyBDaGVjayBpZiBjb25zZW50IGJhbm5lciBzaG91bGQgYmUgc2hvd25cbmV4cG9ydCBjb25zdCBzaG91bGRTaG93Q29uc2VudEJhbm5lciA9ICgpOiBib29sZWFuID0+IHtcbiAgcmV0dXJuIGdldENvbnNlbnRGcm9tU3RvcmFnZSgpID09PSBudWxsXG59XG5cbi8vIEFjY2VwdCBhbGwgY29va2llc1xuZXhwb3J0IGNvbnN0IGFjY2VwdEFsbENvb2tpZXMgPSAoKTogdm9pZCA9PiB7XG4gIGNvbnN0IGNvbnNlbnQ6IENvb2tpZUNvbnNlbnQgPSB7XG4gICAgbmVjZXNzYXJ5OiB0cnVlLFxuICAgIGFuYWx5dGljczogdHJ1ZSxcbiAgICBtYXJrZXRpbmc6IHRydWUsXG4gICAgdGltZXN0YW1wOiBEYXRlLm5vdygpLFxuICAgIHZlcnNpb246IENPT0tJRV9DT05TRU5UX1ZFUlNJT04sXG4gIH1cbiAgXG4gIHNhdmVDb25zZW50VG9TdG9yYWdlKGNvbnNlbnQpXG59XG5cbi8vIEFjY2VwdCBvbmx5IG5lY2Vzc2FyeSBjb29raWVzXG5leHBvcnQgY29uc3QgYWNjZXB0TmVjZXNzYXJ5Q29va2llcyA9ICgpOiB2b2lkID0+IHtcbiAgc2F2ZUNvbnNlbnRUb1N0b3JhZ2UoZGVmYXVsdENvbnNlbnQpXG59XG5cbi8vIFVwZGF0ZSBzcGVjaWZpYyBjb25zZW50IHByZWZlcmVuY2VzXG5leHBvcnQgY29uc3QgdXBkYXRlQ29uc2VudCA9ICh1cGRhdGVzOiBQYXJ0aWFsPE9taXQ8Q29va2llQ29uc2VudCwgJ3RpbWVzdGFtcCcgfCAndmVyc2lvbic+Pik6IHZvaWQgPT4ge1xuICBjb25zdCBjdXJyZW50Q29uc2VudCA9IGdldENvbnNlbnRGcm9tU3RvcmFnZSgpIHx8IGRlZmF1bHRDb25zZW50XG4gIFxuICBjb25zdCBuZXdDb25zZW50OiBDb29raWVDb25zZW50ID0ge1xuICAgIC4uLmN1cnJlbnRDb25zZW50LFxuICAgIC4uLnVwZGF0ZXMsXG4gICAgbmVjZXNzYXJ5OiB0cnVlLCAvLyBBbHdheXMgdHJ1ZVxuICAgIHRpbWVzdGFtcDogRGF0ZS5ub3coKSxcbiAgICB2ZXJzaW9uOiBDT09LSUVfQ09OU0VOVF9WRVJTSU9OLFxuICB9XG4gIFxuICBzYXZlQ29uc2VudFRvU3RvcmFnZShuZXdDb25zZW50KVxufVxuXG4vLyBDbGVhciBhbGwgY29uc2VudCAoZm9yIHRlc3Rpbmcgb3IgcmVzZXQpXG5leHBvcnQgY29uc3QgY2xlYXJDb25zZW50ID0gKCk6IHZvaWQgPT4ge1xuICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHJldHVyblxuICBcbiAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oQ09PS0lFX0NPTlNFTlRfS0VZKVxuICB3aW5kb3cuZGlzcGF0Y2hFdmVudChuZXcgQ3VzdG9tRXZlbnQoJ2Nvb2tpZUNvbnNlbnRDaGFuZ2VkJywge1xuICAgIGRldGFpbDogbnVsbFxuICB9KSlcbn1cblxuLy8gR2V0IGNvbnNlbnQgZXhwaXJ5IGRhdGUgKDEgeWVhciBmcm9tIGNvbnNlbnQgZGF0ZSlcbmV4cG9ydCBjb25zdCBnZXRDb25zZW50RXhwaXJ5RGF0ZSA9ICgpOiBEYXRlIHwgbnVsbCA9PiB7XG4gIGNvbnN0IGNvbnNlbnQgPSBnZXRDb25zZW50RnJvbVN0b3JhZ2UoKVxuICBpZiAoIWNvbnNlbnQpIHJldHVybiBudWxsXG4gIFxuICBjb25zdCBleHBpcnlEYXRlID0gbmV3IERhdGUoY29uc2VudC50aW1lc3RhbXApXG4gIGV4cGlyeURhdGUuc2V0RnVsbFllYXIoZXhwaXJ5RGF0ZS5nZXRGdWxsWWVhcigpICsgMSlcbiAgXG4gIHJldHVybiBleHBpcnlEYXRlXG59XG5cbi8vIENoZWNrIGlmIGNvbnNlbnQgaGFzIGV4cGlyZWRcbmV4cG9ydCBjb25zdCBpc0NvbnNlbnRFeHBpcmVkID0gKCk6IGJvb2xlYW4gPT4ge1xuICBjb25zdCBleHBpcnlEYXRlID0gZ2V0Q29uc2VudEV4cGlyeURhdGUoKVxuICBpZiAoIWV4cGlyeURhdGUpIHJldHVybiB0cnVlXG4gIFxuICByZXR1cm4gbmV3IERhdGUoKSA+IGV4cGlyeURhdGVcbn1cblxuLy8gQ29va2llIGNhdGVnb3JpZXMgaW5mb3JtYXRpb25cbmV4cG9ydCBjb25zdCBjb29raWVDYXRlZ29yaWVzID0ge1xuICBuZWNlc3Nhcnk6IHtcbiAgICBuYW1lOiAnTmVjZXNzYXJ5JyxcbiAgICBkZXNjcmlwdGlvbjogJ1RoZXNlIGNvb2tpZXMgYXJlIGVzc2VudGlhbCBmb3IgdGhlIHdlYnNpdGUgdG8gZnVuY3Rpb24gcHJvcGVybHkuIFRoZXkgZW5hYmxlIGJhc2ljIGZlYXR1cmVzIGxpa2UgcGFnZSBuYXZpZ2F0aW9uIGFuZCBhY2Nlc3MgdG8gc2VjdXJlIGFyZWFzLicsXG4gICAgZXhhbXBsZXM6IFsnU2Vzc2lvbiBjb29raWVzJywgJ0F1dGhlbnRpY2F0aW9uIHRva2VucycsICdTZWN1cml0eSBjb29raWVzJ10sXG4gICAgY2FuRGlzYWJsZTogZmFsc2UsXG4gIH0sXG4gIGFuYWx5dGljczoge1xuICAgIG5hbWU6ICdBbmFseXRpY3MnLFxuICAgIGRlc2NyaXB0aW9uOiAnVGhlc2UgY29va2llcyBoZWxwIHVzIHVuZGVyc3RhbmQgaG93IHZpc2l0b3JzIGludGVyYWN0IHdpdGggb3VyIHdlYnNpdGUgYnkgY29sbGVjdGluZyBhbmQgcmVwb3J0aW5nIGluZm9ybWF0aW9uIGFub255bW91c2x5LicsXG4gICAgZXhhbXBsZXM6IFsnR29vZ2xlIEFuYWx5dGljcycsICdQYWdlIHZpZXcgdHJhY2tpbmcnLCAnVXNlciBiZWhhdmlvciBhbmFseXNpcyddLFxuICAgIGNhbkRpc2FibGU6IHRydWUsXG4gIH0sXG4gIG1hcmtldGluZzoge1xuICAgIG5hbWU6ICdNYXJrZXRpbmcnLFxuICAgIGRlc2NyaXB0aW9uOiAnVGhlc2UgY29va2llcyBhcmUgdXNlZCB0byB0cmFjayB2aXNpdG9ycyBhY3Jvc3Mgd2Vic2l0ZXMgdG8gZGlzcGxheSByZWxldmFudCBhZHZlcnRpc2VtZW50cyBhbmQgbWFya2V0aW5nIGNvbnRlbnQuJyxcbiAgICBleGFtcGxlczogWydTb2NpYWwgbWVkaWEgcGl4ZWxzJywgJ0FkdmVydGlzaW5nIGNvb2tpZXMnLCAnUmV0YXJnZXRpbmcgY29va2llcyddLFxuICAgIGNhbkRpc2FibGU6IHRydWUsXG4gIH0sXG59XG5cbi8vIEluaXRpYWxpemUgY29uc2VudCBtb2RlIGZvciBHb29nbGUgQW5hbHl0aWNzXG5leHBvcnQgY29uc3QgaW5pdGlhbGl6ZUNvbnNlbnRNb2RlID0gKCk6IHZvaWQgPT4ge1xuICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHJldHVyblxuXG4gIGNvbnN0IGNvbnNlbnQgPSBnZXRDb25zZW50RnJvbVN0b3JhZ2UoKVxuICBcbiAgLy8gU2V0IHVwIEdvb2dsZSBDb25zZW50IE1vZGVcbiAgaWYgKHdpbmRvdy5ndGFnKSB7XG4gICAgd2luZG93Lmd0YWcoJ2NvbnNlbnQnLCAnZGVmYXVsdCcsIHtcbiAgICAgIGFuYWx5dGljc19zdG9yYWdlOiBjb25zZW50Py5hbmFseXRpY3MgPyAnZ3JhbnRlZCcgOiAnZGVuaWVkJyxcbiAgICAgIGFkX3N0b3JhZ2U6IGNvbnNlbnQ/Lm1hcmtldGluZyA/ICdncmFudGVkJyA6ICdkZW5pZWQnLFxuICAgICAgYWRfdXNlcl9kYXRhOiBjb25zZW50Py5tYXJrZXRpbmcgPyAnZ3JhbnRlZCcgOiAnZGVuaWVkJyxcbiAgICAgIGFkX3BlcnNvbmFsaXphdGlvbjogY29uc2VudD8ubWFya2V0aW5nID8gJ2dyYW50ZWQnIDogJ2RlbmllZCcsXG4gICAgfSlcbiAgfVxufVxuXG4vLyBVcGRhdGUgR29vZ2xlIENvbnNlbnQgTW9kZSB3aGVuIGNvbnNlbnQgY2hhbmdlc1xuZXhwb3J0IGNvbnN0IHVwZGF0ZUNvbnNlbnRNb2RlID0gKGNvbnNlbnQ6IENvb2tpZUNvbnNlbnQpOiB2b2lkID0+IHtcbiAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnIHx8ICF3aW5kb3cuZ3RhZykgcmV0dXJuXG5cbiAgd2luZG93Lmd0YWcoJ2NvbnNlbnQnLCAndXBkYXRlJywge1xuICAgIGFuYWx5dGljc19zdG9yYWdlOiBjb25zZW50LmFuYWx5dGljcyA/ICdncmFudGVkJyA6ICdkZW5pZWQnLFxuICAgIGFkX3N0b3JhZ2U6IGNvbnNlbnQubWFya2V0aW5nID8gJ2dyYW50ZWQnIDogJ2RlbmllZCcsXG4gICAgYWRfdXNlcl9kYXRhOiBjb25zZW50Lm1hcmtldGluZyA/ICdncmFudGVkJyA6ICdkZW5pZWQnLFxuICAgIGFkX3BlcnNvbmFsaXphdGlvbjogY29uc2VudC5tYXJrZXRpbmcgPyAnZ3JhbnRlZCcgOiAnZGVuaWVkJyxcbiAgfSlcbn1cbiJdLCJuYW1lcyI6WyJDT09LSUVfQ09OU0VOVF9WRVJTSU9OIiwiQ09PS0lFX0NPTlNFTlRfS0VZIiwiZGVmYXVsdENvbnNlbnQiLCJuZWNlc3NhcnkiLCJhbmFseXRpY3MiLCJtYXJrZXRpbmciLCJ0aW1lc3RhbXAiLCJEYXRlIiwibm93IiwidmVyc2lvbiIsImdldENvbnNlbnRGcm9tU3RvcmFnZSIsInN0b3JlZCIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJjb25zZW50IiwiSlNPTiIsInBhcnNlIiwiZXJyb3IiLCJjb25zb2xlIiwic2F2ZUNvbnNlbnRUb1N0b3JhZ2UiLCJjb25zZW50V2l0aFRpbWVzdGFtcCIsInNldEl0ZW0iLCJzdHJpbmdpZnkiLCJ3aW5kb3ciLCJkaXNwYXRjaEV2ZW50IiwiQ3VzdG9tRXZlbnQiLCJkZXRhaWwiLCJoYXNDb25zZW50IiwiY2F0ZWdvcnkiLCJzaG91bGRTaG93Q29uc2VudEJhbm5lciIsImFjY2VwdEFsbENvb2tpZXMiLCJhY2NlcHROZWNlc3NhcnlDb29raWVzIiwidXBkYXRlQ29uc2VudCIsInVwZGF0ZXMiLCJjdXJyZW50Q29uc2VudCIsIm5ld0NvbnNlbnQiLCJjbGVhckNvbnNlbnQiLCJyZW1vdmVJdGVtIiwiZ2V0Q29uc2VudEV4cGlyeURhdGUiLCJleHBpcnlEYXRlIiwic2V0RnVsbFllYXIiLCJnZXRGdWxsWWVhciIsImlzQ29uc2VudEV4cGlyZWQiLCJjb29raWVDYXRlZ29yaWVzIiwibmFtZSIsImRlc2NyaXB0aW9uIiwiZXhhbXBsZXMiLCJjYW5EaXNhYmxlIiwiaW5pdGlhbGl6ZUNvbnNlbnRNb2RlIiwiZ3RhZyIsImFuYWx5dGljc19zdG9yYWdlIiwiYWRfc3RvcmFnZSIsImFkX3VzZXJfZGF0YSIsImFkX3BlcnNvbmFsaXphdGlvbiIsInVwZGF0ZUNvbnNlbnRNb2RlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/cookies.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   extractTextFromHtml: () => (/* binding */ extractTextFromHtml),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatFileSize: () => (/* binding */ formatFileSize),\n/* harmony export */   formatRelativeTime: () => (/* binding */ formatRelativeTime),\n/* harmony export */   generateExcerpt: () => (/* binding */ generateExcerpt),\n/* harmony export */   generateRandomString: () => (/* binding */ generateRandomString),\n/* harmony export */   getFileExtension: () => (/* binding */ getFileExtension),\n/* harmony export */   isImageFile: () => (/* binding */ isImageFile),\n/* harmony export */   isValidUrl: () => (/* binding */ isValidUrl),\n/* harmony export */   isVideoFile: () => (/* binding */ isVideoFile),\n/* harmony export */   slugify: () => (/* binding */ slugify),\n/* harmony export */   truncateText: () => (/* binding */ truncateText)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction slugify(text) {\n    return text.toLowerCase().replace(/[^\\w\\s-]/g, '') // Remove special characters\n    .replace(/[\\s_-]+/g, '-') // Replace spaces and underscores with hyphens\n    .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens\n    ;\n}\nfunction formatDate(date) {\n    const d = new Date(date);\n    return d.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric'\n    });\n}\nfunction formatRelativeTime(date) {\n    const d = new Date(date);\n    const now = new Date();\n    const diffInSeconds = Math.floor((now.getTime() - d.getTime()) / 1000);\n    if (diffInSeconds < 60) return 'just now';\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;\n    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;\n    if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} days ago`;\n    return formatDate(d);\n}\nfunction truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.slice(0, maxLength).trim() + '...';\n}\nfunction extractTextFromHtml(html) {\n    return html.replace(/<[^>]*>/g, '').trim();\n}\nfunction generateExcerpt(content, maxLength = 160) {\n    const text = extractTextFromHtml(content);\n    return truncateText(text, maxLength);\n}\nfunction isValidUrl(url) {\n    try {\n        new URL(url);\n        return true;\n    } catch  {\n        return false;\n    }\n}\nfunction getFileExtension(filename) {\n    return filename.split('.').pop()?.toLowerCase() || '';\n}\nfunction formatFileSize(bytes) {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = [\n        'Bytes',\n        'KB',\n        'MB',\n        'GB'\n    ];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n}\nfunction isImageFile(filename) {\n    const imageExtensions = [\n        'jpg',\n        'jpeg',\n        'png',\n        'gif',\n        'webp',\n        'svg'\n    ];\n    const extension = getFileExtension(filename);\n    return imageExtensions.includes(extension);\n}\nfunction isVideoFile(filename) {\n    const videoExtensions = [\n        'mp4',\n        'webm',\n        'ogg',\n        'avi',\n        'mov'\n    ];\n    const extension = getFileExtension(filename);\n    return videoExtensions.includes(extension);\n}\nfunction generateRandomString(length) {\n    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';\n    let result = '';\n    for(let i = 0; i < length; i++){\n        result += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    return result;\n}\nfunction debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBNEM7QUFDSjtBQUVqQyxTQUFTRSxHQUFHLEdBQUdDLE1BQW9CO0lBQ3hDLE9BQU9GLHVEQUFPQSxDQUFDRCwwQ0FBSUEsQ0FBQ0c7QUFDdEI7QUFFTyxTQUFTQyxRQUFRQyxJQUFZO0lBQ2xDLE9BQU9BLEtBQ0pDLFdBQVcsR0FDWEMsT0FBTyxDQUFDLGFBQWEsSUFBSSw0QkFBNEI7S0FDckRBLE9BQU8sQ0FBQyxZQUFZLEtBQUssOENBQThDO0tBQ3ZFQSxPQUFPLENBQUMsWUFBWSxJQUFJLGtDQUFrQzs7QUFDL0Q7QUFFTyxTQUFTQyxXQUFXQyxJQUFtQjtJQUM1QyxNQUFNQyxJQUFJLElBQUlDLEtBQUtGO0lBQ25CLE9BQU9DLEVBQUVFLGtCQUFrQixDQUFDLFNBQVM7UUFDbkNDLE1BQU07UUFDTkMsT0FBTztRQUNQQyxLQUFLO0lBQ1A7QUFDRjtBQUVPLFNBQVNDLG1CQUFtQlAsSUFBbUI7SUFDcEQsTUFBTUMsSUFBSSxJQUFJQyxLQUFLRjtJQUNuQixNQUFNUSxNQUFNLElBQUlOO0lBQ2hCLE1BQU1PLGdCQUFnQkMsS0FBS0MsS0FBSyxDQUFDLENBQUNILElBQUlJLE9BQU8sS0FBS1gsRUFBRVcsT0FBTyxFQUFDLElBQUs7SUFFakUsSUFBSUgsZ0JBQWdCLElBQUksT0FBTztJQUMvQixJQUFJQSxnQkFBZ0IsTUFBTSxPQUFPLEdBQUdDLEtBQUtDLEtBQUssQ0FBQ0YsZ0JBQWdCLElBQUksWUFBWSxDQUFDO0lBQ2hGLElBQUlBLGdCQUFnQixPQUFPLE9BQU8sR0FBR0MsS0FBS0MsS0FBSyxDQUFDRixnQkFBZ0IsTUFBTSxVQUFVLENBQUM7SUFDakYsSUFBSUEsZ0JBQWdCLFNBQVMsT0FBTyxHQUFHQyxLQUFLQyxLQUFLLENBQUNGLGdCQUFnQixPQUFPLFNBQVMsQ0FBQztJQUVuRixPQUFPVixXQUFXRTtBQUNwQjtBQUVPLFNBQVNZLGFBQWFqQixJQUFZLEVBQUVrQixTQUFpQjtJQUMxRCxJQUFJbEIsS0FBS21CLE1BQU0sSUFBSUQsV0FBVyxPQUFPbEI7SUFDckMsT0FBT0EsS0FBS29CLEtBQUssQ0FBQyxHQUFHRixXQUFXRyxJQUFJLEtBQUs7QUFDM0M7QUFFTyxTQUFTQyxvQkFBb0JDLElBQVk7SUFDOUMsT0FBT0EsS0FBS3JCLE9BQU8sQ0FBQyxZQUFZLElBQUltQixJQUFJO0FBQzFDO0FBRU8sU0FBU0csZ0JBQWdCQyxPQUFlLEVBQUVQLFlBQW9CLEdBQUc7SUFDdEUsTUFBTWxCLE9BQU9zQixvQkFBb0JHO0lBQ2pDLE9BQU9SLGFBQWFqQixNQUFNa0I7QUFDNUI7QUFFTyxTQUFTUSxXQUFXQyxHQUFXO0lBQ3BDLElBQUk7UUFDRixJQUFJQyxJQUFJRDtRQUNSLE9BQU87SUFDVCxFQUFFLE9BQU07UUFDTixPQUFPO0lBQ1Q7QUFDRjtBQUVPLFNBQVNFLGlCQUFpQkMsUUFBZ0I7SUFDL0MsT0FBT0EsU0FBU0MsS0FBSyxDQUFDLEtBQUtDLEdBQUcsSUFBSS9CLGlCQUFpQjtBQUNyRDtBQUVPLFNBQVNnQyxlQUFlQyxLQUFhO0lBQzFDLElBQUlBLFVBQVUsR0FBRyxPQUFPO0lBRXhCLE1BQU1DLElBQUk7SUFDVixNQUFNQyxRQUFRO1FBQUM7UUFBUztRQUFNO1FBQU07S0FBSztJQUN6QyxNQUFNQyxJQUFJdkIsS0FBS0MsS0FBSyxDQUFDRCxLQUFLd0IsR0FBRyxDQUFDSixTQUFTcEIsS0FBS3dCLEdBQUcsQ0FBQ0g7SUFFaEQsT0FBT0ksV0FBVyxDQUFDTCxRQUFRcEIsS0FBSzBCLEdBQUcsQ0FBQ0wsR0FBR0UsRUFBQyxFQUFHSSxPQUFPLENBQUMsTUFBTSxNQUFNTCxLQUFLLENBQUNDLEVBQUU7QUFDekU7QUFFTyxTQUFTSyxZQUFZWixRQUFnQjtJQUMxQyxNQUFNYSxrQkFBa0I7UUFBQztRQUFPO1FBQVE7UUFBTztRQUFPO1FBQVE7S0FBTTtJQUNwRSxNQUFNQyxZQUFZZixpQkFBaUJDO0lBQ25DLE9BQU9hLGdCQUFnQkUsUUFBUSxDQUFDRDtBQUNsQztBQUVPLFNBQVNFLFlBQVloQixRQUFnQjtJQUMxQyxNQUFNaUIsa0JBQWtCO1FBQUM7UUFBTztRQUFRO1FBQU87UUFBTztLQUFNO0lBQzVELE1BQU1ILFlBQVlmLGlCQUFpQkM7SUFDbkMsT0FBT2lCLGdCQUFnQkYsUUFBUSxDQUFDRDtBQUNsQztBQUVPLFNBQVNJLHFCQUFxQjdCLE1BQWM7SUFDakQsTUFBTThCLFFBQVE7SUFDZCxJQUFJQyxTQUFTO0lBQ2IsSUFBSyxJQUFJYixJQUFJLEdBQUdBLElBQUlsQixRQUFRa0IsSUFBSztRQUMvQmEsVUFBVUQsTUFBTUUsTUFBTSxDQUFDckMsS0FBS0MsS0FBSyxDQUFDRCxLQUFLc0MsTUFBTSxLQUFLSCxNQUFNOUIsTUFBTTtJQUNoRTtJQUNBLE9BQU8rQjtBQUNUO0FBRU8sU0FBU0csU0FDZEMsSUFBTyxFQUNQQyxJQUFZO0lBRVosSUFBSUM7SUFDSixPQUFPLENBQUMsR0FBR0M7UUFDVEMsYUFBYUY7UUFDYkEsVUFBVUcsV0FBVyxJQUFNTCxRQUFRRyxPQUFPRjtJQUM1QztBQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFV0ZW50ZVxcRGVza3RvcFxcQm9pbGVycGxhdGVcXHNyY1xcbGliXFx1dGlscy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB0eXBlIENsYXNzVmFsdWUsIGNsc3ggfSBmcm9tIFwiY2xzeFwiXG5pbXBvcnQgeyB0d01lcmdlIH0gZnJvbSBcInRhaWx3aW5kLW1lcmdlXCJcblxuZXhwb3J0IGZ1bmN0aW9uIGNuKC4uLmlucHV0czogQ2xhc3NWYWx1ZVtdKSB7XG4gIHJldHVybiB0d01lcmdlKGNsc3goaW5wdXRzKSlcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIHNsdWdpZnkodGV4dDogc3RyaW5nKTogc3RyaW5nIHtcbiAgcmV0dXJuIHRleHRcbiAgICAudG9Mb3dlckNhc2UoKVxuICAgIC5yZXBsYWNlKC9bXlxcd1xccy1dL2csICcnKSAvLyBSZW1vdmUgc3BlY2lhbCBjaGFyYWN0ZXJzXG4gICAgLnJlcGxhY2UoL1tcXHNfLV0rL2csICctJykgLy8gUmVwbGFjZSBzcGFjZXMgYW5kIHVuZGVyc2NvcmVzIHdpdGggaHlwaGVuc1xuICAgIC5yZXBsYWNlKC9eLSt8LSskL2csICcnKSAvLyBSZW1vdmUgbGVhZGluZy90cmFpbGluZyBoeXBoZW5zXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBmb3JtYXREYXRlKGRhdGU6IERhdGUgfCBzdHJpbmcpOiBzdHJpbmcge1xuICBjb25zdCBkID0gbmV3IERhdGUoZGF0ZSlcbiAgcmV0dXJuIGQudG9Mb2NhbGVEYXRlU3RyaW5nKCdlbi1VUycsIHtcbiAgICB5ZWFyOiAnbnVtZXJpYycsXG4gICAgbW9udGg6ICdsb25nJyxcbiAgICBkYXk6ICdudW1lcmljJ1xuICB9KVxufVxuXG5leHBvcnQgZnVuY3Rpb24gZm9ybWF0UmVsYXRpdmVUaW1lKGRhdGU6IERhdGUgfCBzdHJpbmcpOiBzdHJpbmcge1xuICBjb25zdCBkID0gbmV3IERhdGUoZGF0ZSlcbiAgY29uc3Qgbm93ID0gbmV3IERhdGUoKVxuICBjb25zdCBkaWZmSW5TZWNvbmRzID0gTWF0aC5mbG9vcigobm93LmdldFRpbWUoKSAtIGQuZ2V0VGltZSgpKSAvIDEwMDApXG5cbiAgaWYgKGRpZmZJblNlY29uZHMgPCA2MCkgcmV0dXJuICdqdXN0IG5vdydcbiAgaWYgKGRpZmZJblNlY29uZHMgPCAzNjAwKSByZXR1cm4gYCR7TWF0aC5mbG9vcihkaWZmSW5TZWNvbmRzIC8gNjApfSBtaW51dGVzIGFnb2BcbiAgaWYgKGRpZmZJblNlY29uZHMgPCA4NjQwMCkgcmV0dXJuIGAke01hdGguZmxvb3IoZGlmZkluU2Vjb25kcyAvIDM2MDApfSBob3VycyBhZ29gXG4gIGlmIChkaWZmSW5TZWNvbmRzIDwgMjU5MjAwMCkgcmV0dXJuIGAke01hdGguZmxvb3IoZGlmZkluU2Vjb25kcyAvIDg2NDAwKX0gZGF5cyBhZ29gXG4gIFxuICByZXR1cm4gZm9ybWF0RGF0ZShkKVxufVxuXG5leHBvcnQgZnVuY3Rpb24gdHJ1bmNhdGVUZXh0KHRleHQ6IHN0cmluZywgbWF4TGVuZ3RoOiBudW1iZXIpOiBzdHJpbmcge1xuICBpZiAodGV4dC5sZW5ndGggPD0gbWF4TGVuZ3RoKSByZXR1cm4gdGV4dFxuICByZXR1cm4gdGV4dC5zbGljZSgwLCBtYXhMZW5ndGgpLnRyaW0oKSArICcuLi4nXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBleHRyYWN0VGV4dEZyb21IdG1sKGh0bWw6IHN0cmluZyk6IHN0cmluZyB7XG4gIHJldHVybiBodG1sLnJlcGxhY2UoLzxbXj5dKj4vZywgJycpLnRyaW0oKVxufVxuXG5leHBvcnQgZnVuY3Rpb24gZ2VuZXJhdGVFeGNlcnB0KGNvbnRlbnQ6IHN0cmluZywgbWF4TGVuZ3RoOiBudW1iZXIgPSAxNjApOiBzdHJpbmcge1xuICBjb25zdCB0ZXh0ID0gZXh0cmFjdFRleHRGcm9tSHRtbChjb250ZW50KVxuICByZXR1cm4gdHJ1bmNhdGVUZXh0KHRleHQsIG1heExlbmd0aClcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGlzVmFsaWRVcmwodXJsOiBzdHJpbmcpOiBib29sZWFuIHtcbiAgdHJ5IHtcbiAgICBuZXcgVVJMKHVybClcbiAgICByZXR1cm4gdHJ1ZVxuICB9IGNhdGNoIHtcbiAgICByZXR1cm4gZmFsc2VcbiAgfVxufVxuXG5leHBvcnQgZnVuY3Rpb24gZ2V0RmlsZUV4dGVuc2lvbihmaWxlbmFtZTogc3RyaW5nKTogc3RyaW5nIHtcbiAgcmV0dXJuIGZpbGVuYW1lLnNwbGl0KCcuJykucG9wKCk/LnRvTG93ZXJDYXNlKCkgfHwgJydcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGZvcm1hdEZpbGVTaXplKGJ5dGVzOiBudW1iZXIpOiBzdHJpbmcge1xuICBpZiAoYnl0ZXMgPT09IDApIHJldHVybiAnMCBCeXRlcydcbiAgXG4gIGNvbnN0IGsgPSAxMDI0XG4gIGNvbnN0IHNpemVzID0gWydCeXRlcycsICdLQicsICdNQicsICdHQiddXG4gIGNvbnN0IGkgPSBNYXRoLmZsb29yKE1hdGgubG9nKGJ5dGVzKSAvIE1hdGgubG9nKGspKVxuICBcbiAgcmV0dXJuIHBhcnNlRmxvYXQoKGJ5dGVzIC8gTWF0aC5wb3coaywgaSkpLnRvRml4ZWQoMikpICsgJyAnICsgc2l6ZXNbaV1cbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGlzSW1hZ2VGaWxlKGZpbGVuYW1lOiBzdHJpbmcpOiBib29sZWFuIHtcbiAgY29uc3QgaW1hZ2VFeHRlbnNpb25zID0gWydqcGcnLCAnanBlZycsICdwbmcnLCAnZ2lmJywgJ3dlYnAnLCAnc3ZnJ11cbiAgY29uc3QgZXh0ZW5zaW9uID0gZ2V0RmlsZUV4dGVuc2lvbihmaWxlbmFtZSlcbiAgcmV0dXJuIGltYWdlRXh0ZW5zaW9ucy5pbmNsdWRlcyhleHRlbnNpb24pXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBpc1ZpZGVvRmlsZShmaWxlbmFtZTogc3RyaW5nKTogYm9vbGVhbiB7XG4gIGNvbnN0IHZpZGVvRXh0ZW5zaW9ucyA9IFsnbXA0JywgJ3dlYm0nLCAnb2dnJywgJ2F2aScsICdtb3YnXVxuICBjb25zdCBleHRlbnNpb24gPSBnZXRGaWxlRXh0ZW5zaW9uKGZpbGVuYW1lKVxuICByZXR1cm4gdmlkZW9FeHRlbnNpb25zLmluY2x1ZGVzKGV4dGVuc2lvbilcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGdlbmVyYXRlUmFuZG9tU3RyaW5nKGxlbmd0aDogbnVtYmVyKTogc3RyaW5nIHtcbiAgY29uc3QgY2hhcnMgPSAnQUJDREVGR0hJSktMTU5PUFFSU1RVVldYWVphYmNkZWZnaGlqa2xtbm9wcXJzdHV2d3h5ejAxMjM0NTY3ODknXG4gIGxldCByZXN1bHQgPSAnJ1xuICBmb3IgKGxldCBpID0gMDsgaSA8IGxlbmd0aDsgaSsrKSB7XG4gICAgcmVzdWx0ICs9IGNoYXJzLmNoYXJBdChNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiBjaGFycy5sZW5ndGgpKVxuICB9XG4gIHJldHVybiByZXN1bHRcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGRlYm91bmNlPFQgZXh0ZW5kcyAoLi4uYXJnczogYW55W10pID0+IGFueT4oXG4gIGZ1bmM6IFQsXG4gIHdhaXQ6IG51bWJlclxuKTogKC4uLmFyZ3M6IFBhcmFtZXRlcnM8VD4pID0+IHZvaWQge1xuICBsZXQgdGltZW91dDogTm9kZUpTLlRpbWVvdXRcbiAgcmV0dXJuICguLi5hcmdzOiBQYXJhbWV0ZXJzPFQ+KSA9PiB7XG4gICAgY2xlYXJUaW1lb3V0KHRpbWVvdXQpXG4gICAgdGltZW91dCA9IHNldFRpbWVvdXQoKCkgPT4gZnVuYyguLi5hcmdzKSwgd2FpdClcbiAgfVxufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiLCJzbHVnaWZ5IiwidGV4dCIsInRvTG93ZXJDYXNlIiwicmVwbGFjZSIsImZvcm1hdERhdGUiLCJkYXRlIiwiZCIsIkRhdGUiLCJ0b0xvY2FsZURhdGVTdHJpbmciLCJ5ZWFyIiwibW9udGgiLCJkYXkiLCJmb3JtYXRSZWxhdGl2ZVRpbWUiLCJub3ciLCJkaWZmSW5TZWNvbmRzIiwiTWF0aCIsImZsb29yIiwiZ2V0VGltZSIsInRydW5jYXRlVGV4dCIsIm1heExlbmd0aCIsImxlbmd0aCIsInNsaWNlIiwidHJpbSIsImV4dHJhY3RUZXh0RnJvbUh0bWwiLCJodG1sIiwiZ2VuZXJhdGVFeGNlcnB0IiwiY29udGVudCIsImlzVmFsaWRVcmwiLCJ1cmwiLCJVUkwiLCJnZXRGaWxlRXh0ZW5zaW9uIiwiZmlsZW5hbWUiLCJzcGxpdCIsInBvcCIsImZvcm1hdEZpbGVTaXplIiwiYnl0ZXMiLCJrIiwic2l6ZXMiLCJpIiwibG9nIiwicGFyc2VGbG9hdCIsInBvdyIsInRvRml4ZWQiLCJpc0ltYWdlRmlsZSIsImltYWdlRXh0ZW5zaW9ucyIsImV4dGVuc2lvbiIsImluY2x1ZGVzIiwiaXNWaWRlb0ZpbGUiLCJ2aWRlb0V4dGVuc2lvbnMiLCJnZW5lcmF0ZVJhbmRvbVN0cmluZyIsImNoYXJzIiwicmVzdWx0IiwiY2hhckF0IiwicmFuZG9tIiwiZGVib3VuY2UiLCJmdW5jIiwid2FpdCIsInRpbWVvdXQiLCJhcmdzIiwiY2xlYXJUaW1lb3V0Iiwic2V0VGltZW91dCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/class-variance-authority","vendor-chunks/@swc","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fabout%2Fpage&page=%2Fabout%2Fpage&appPaths=%2Fabout%2Fpage&pagePath=private-next-app-dir%2Fabout%2Fpage.tsx&appDir=C%3A%5CUsers%5CUtente%5CDesktop%5CBoilerplate%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUtente%5CDesktop%5CBoilerplate&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();